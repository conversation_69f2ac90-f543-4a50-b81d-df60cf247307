#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

#%%
print("=== 数据问题诊断 ===")

# 重现原始代码的数据处理流程
df = pd.read_csv('data.csv')
print(f"原始数据形状: {df.shape}")

# 数据预处理（重现原始代码）
df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')
df['year'] = df['date'].dt.year
df['month'] = df['date'].dt.month
df['day_of_year'] = df['date'].dt.dayofyear

df['is_renovated'] = (df['yr_renovated'] > 0).astype(int)
df['years_since_renovation'] = np.where(df['yr_renovated'] > 0, 
                                       df['year'] - df['yr_renovated'], 
                                       df['year'] - df['yr_built'])

df['house_age'] = df['year'] - df['yr_built']
df['price_per_sqft'] = df['price'] / df['sqft_living']

# 异常值处理（重现原始代码）
price_99_5th = df['price'].quantile(0.995)
price_0_5th = df['price'].quantile(0.005)
df_clean = df[(df['price'] >= price_0_5th) & (df['price'] <= price_99_5th)]

living_99_5th = df['sqft_living'].quantile(0.995)
living_0_5th = df['sqft_living'].quantile(0.005)
df_clean = df_clean[(df_clean['sqft_living'] >= living_0_5th) & (df_clean['sqft_living'] <= living_99_5th)]

df_clean = df_clean[df_clean['price'] > 0]
df_clean = df_clean[df_clean['sqft_living'] > 0]
df_clean = df_clean[df_clean['sqft_lot'] > 0]

print(f"清理后数据形状: {df_clean.shape}")

#%%
# 问题1：检查特征选择
print("\n=== 问题1：特征选择分析 ===")

feature_columns = ['sqft_lot', 'sqft_living', 'grade', 'lat', 'long', 
                  'yr_built', 'house_age', 'is_renovated', 'years_since_renovation',
                  'year', 'month']

X = df_clean[feature_columns].copy()
y = df_clean['price'].copy()

print(f"特征数量: {X.shape[1]}")
print(f"样本数量: {X.shape[0]}")

# 检查特征与目标变量的相关性
correlation_with_target = X.corrwith(y).abs().sort_values(ascending=False)
print("\n特征与价格的相关性:")
for feature, corr in correlation_with_target.items():
    print(f"  {feature}: {corr:.4f}")

# 识别弱相关特征
weak_features = correlation_with_target[correlation_with_target < 0.1]
if len(weak_features) > 0:
    print(f"\n⚠️ 弱相关特征 (相关性 < 0.1): {weak_features.index.tolist()}")

#%%
# 问题2：检查数据尺度问题
print("\n=== 问题2：数据尺度分析 ===")

print("各特征的数值范围:")
for col in feature_columns:
    print(f"  {col}: {X[col].min():.2f} - {X[col].max():.2f} (std: {X[col].std():.2f})")

print(f"\n目标变量价格范围: ${y.min():,.0f} - ${y.max():,.0f}")
print(f"价格标准差: ${y.std():,.0f}")

# 检查是否有特征的尺度差异过大
feature_ranges = {}
for col in feature_columns:
    feature_ranges[col] = X[col].max() - X[col].min()

max_range = max(feature_ranges.values())
min_range = min(feature_ranges.values())
print(f"\n特征尺度差异: 最大范围 {max_range:.0f} vs 最小范围 {min_range:.2f}")
print(f"尺度差异倍数: {max_range/min_range:.0f}倍")

if max_range/min_range > 1000:
    print("⚠️ 特征尺度差异过大，可能影响模型性能")

#%%
# 问题3：检查多重共线性
print("\n=== 问题3：多重共线性检查 ===")

correlation_matrix = X.corr()
print("特征间相关性矩阵:")

# 找出高度相关的特征对
high_corr_pairs = []
for i in range(len(correlation_matrix.columns)):
    for j in range(i+1, len(correlation_matrix.columns)):
        corr_val = abs(correlation_matrix.iloc[i, j])
        if corr_val > 0.8:
            high_corr_pairs.append((
                correlation_matrix.columns[i], 
                correlation_matrix.columns[j], 
                corr_val
            ))

if high_corr_pairs:
    print("\n⚠️ 高度相关的特征对 (相关性 > 0.8):")
    for feat1, feat2, corr in high_corr_pairs:
        print(f"  {feat1} - {feat2}: {corr:.4f}")
else:
    print("\n✅ 没有发现严重的多重共线性问题")

#%%
# 问题4：检查数据分布
print("\n=== 问题4：数据分布分析 ===")

# 检查目标变量分布
from scipy import stats

# 正态性检验
_, p_value = stats.normaltest(y)
print(f"价格正态性检验 p-value: {p_value:.6f}")
if p_value < 0.05:
    print("⚠️ 价格分布不符合正态分布")

# 检查是否需要对数变换
y_log = np.log(y)
_, p_value_log = stats.normaltest(y_log)
print(f"对数价格正态性检验 p-value: {p_value_log:.6f}")

if p_value_log > p_value:
    print("💡 建议对价格进行对数变换")

#%%
# 问题5：重新训练模型进行对比
print("\n=== 问题5：模型性能对比 ===")

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

def evaluate_model(y_true, y_pred, model_name):
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_true, y_pred)
    print(f"{model_name}: RMSE=${rmse:,.0f}, R²={r2:.4f}")
    return rmse, r2

results = {}

# 1. 原始方法（标准化）
scaler_X = StandardScaler()
X_train_scaled = scaler_X.fit_transform(X_train)
X_test_scaled = scaler_X.transform(X_test)

scaler_y = StandardScaler()
y_train_scaled = scaler_y.fit_transform(y_train.values.reshape(-1, 1)).ravel()
y_test_scaled = scaler_y.transform(y_test.values.reshape(-1, 1)).ravel()

lr1 = LinearRegression()
lr1.fit(X_train_scaled, y_train_scaled)
pred1_scaled = lr1.predict(X_test_scaled)
pred1 = scaler_y.inverse_transform(pred1_scaled.reshape(-1, 1)).ravel()
rmse1, r2_1 = evaluate_model(y_test, pred1, "原始方法（标准化）")
results['原始方法'] = {'RMSE': rmse1, 'R2': r2_1}

# 2. 不标准化目标变量
lr2 = LinearRegression()
lr2.fit(X_train_scaled, y_train)  # 只标准化特征，不标准化目标
pred2 = lr2.predict(X_test_scaled)
rmse2, r2_2 = evaluate_model(y_test, pred2, "只标准化特征")
results['只标准化特征'] = {'RMSE': rmse2, 'R2': r2_2}

# 3. 对数变换目标变量
y_train_log = np.log(y_train)
y_test_log = np.log(y_test)

lr3 = LinearRegression()
lr3.fit(X_train_scaled, y_train_log)
pred3_log = lr3.predict(X_test_scaled)
pred3 = np.exp(pred3_log)  # 反变换
rmse3, r2_3 = evaluate_model(y_test, pred3, "对数变换目标变量")
results['对数变换'] = {'RMSE': rmse3, 'R2': r2_3}

# 4. 移除弱相关特征
if len(weak_features) > 0:
    strong_features = [f for f in feature_columns if f not in weak_features.index]
    X_train_strong = X_train[strong_features]
    X_test_strong = X_test[strong_features]
    
    scaler_strong = StandardScaler()
    X_train_strong_scaled = scaler_strong.fit_transform(X_train_strong)
    X_test_strong_scaled = scaler_strong.transform(X_test_strong)
    
    lr4 = LinearRegression()
    lr4.fit(X_train_strong_scaled, y_train)
    pred4 = lr4.predict(X_test_strong_scaled)
    rmse4, r2_4 = evaluate_model(y_test, pred4, "移除弱相关特征")
    results['移除弱特征'] = {'RMSE': rmse4, 'R2': r2_4}

#%%
# 结果总结
print("\n=== 结果总结 ===")
results_df = pd.DataFrame(results).T
print(results_df)

best_method = results_df['RMSE'].idxmin()
print(f"\n最佳方法: {best_method}")
print(f"RMSE: ${results_df.loc[best_method, 'RMSE']:,.0f}")
print(f"R²: {results_df.loc[best_method, 'R2']:.4f}")

# 可视化对比
fig, axes = plt.subplots(1, 2, figsize=(12, 5))

axes[0].bar(results_df.index, results_df['RMSE'])
axes[0].set_title('RMSE比较')
axes[0].set_ylabel('RMSE ($)')
axes[0].tick_params(axis='x', rotation=45)

axes[1].bar(results_df.index, results_df['R2'])
axes[1].set_title('R²比较')
axes[1].set_ylabel('R²')
axes[1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

#%%
# 建议
print("\n=== 改进建议 ===")
print("1. 特征工程：添加更多有意义的特征（如价格/面积比、位置聚类等）")
print("2. 特征选择：移除弱相关特征，保留强相关特征")
if p_value_log > p_value:
    print("3. 目标变量变换：考虑对价格进行对数变换")
print("4. 模型选择：尝试非线性模型（随机森林、梯度提升等）")
print("5. 超参数调优：使用网格搜索或随机搜索优化模型参数")

print("\n诊断完成！")
