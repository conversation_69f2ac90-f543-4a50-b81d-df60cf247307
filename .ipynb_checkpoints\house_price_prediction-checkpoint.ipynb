{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-08-02T06:08:18.700341Z", "start_time": "2025-08-02T06:08:18.686321Z"}}, "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import os\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置支持中文的字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体\n", "plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示为方块的问题\n"], "outputs": [], "execution_count": 1}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# ===== 1. 数据加载和初步探索 =====\n", "print(\"=== 数据加载 ===\")\n", "df = pd.read_csv('data.csv')\n", "print(f\"数据集形状: {df.shape}\")\n", "print(f\"数据集列名: {df.columns.tolist()}\")\n", "print(\"\\n数据集前5行:\")\n", "print(df.head())\n"], "id": "1aff6b030391d644"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 数据基本信息\n", "print(\"\\n=== 数据基本信息 ===\")\n", "print(df.info())\n", "print(\"\\n数据统计描述:\")\n", "print(df.describe())\n"], "id": "2ba32048eb9e8a0d"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# ===== 2. 数据质量检查 =====\n", "print(\"\\n=== 缺失值检查 ===\")\n", "missing_values = df.isnull().sum()\n", "print(\"各列缺失值数量:\")\n", "print(missing_values)\n", "print(f\"\\n总缺失值数量: {missing_values.sum()}\")\n", "print(f\"缺失值比例: {(missing_values.sum() / (df.shape[0] * df.shape[1])) * 100:.2f}%\")\n"], "id": "57fa28f77838dbdb"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 检查重复值\n", "print(\"\\n=== 重复值检查 ===\")\n", "duplicates = df.duplicated().sum()\n", "print(f\"重复行数量: {duplicates}\")\n", "print(f\"重复行比例: {(duplicates / len(df)) * 100:.2f}%\")\n"], "id": "4109420a56d3db14"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# ===== 3. 数据预处理和特征工程 =====\n", "print(\"\\n=== 数据预处理 ===\")\n", "\n", "# 处理日期列\n", "df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')\n", "df['year'] = df['date'].dt.year\n", "df['month'] = df['date'].dt.month\n", "df['day_of_year'] = df['date'].dt.dayofyear\n", "\n", "# 处理yr_renovated列（0表示未翻新）\n", "df['is_renovated'] = (df['yr_renovated'] > 0).astype(int)\n", "df['years_since_renovation'] = np.where(df['yr_renovated'] > 0, \n", "                                       df['year'] - df['yr_renovated'], \n", "                                       df['year'] - df['yr_built'])\n", "\n", "# 房屋年龄\n", "df['house_age'] = df['year'] - df['yr_built']\n", "\n", "# 价格每平方英尺\n", "df['price_per_sqft'] = df['price'] / df['sqft_living']\n", "\n", "print(\"新特征创建完成\")\n", "print(f\"处理后数据形状: {df.shape}\")\n"], "id": "7d318283f230b069"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# ===== 4. 异常值检测和处理 =====\n", "print(\"\\n=== 异常值检测 ===\")\n", "\n", "def detect_outliers_iqr(data, column):\n", "    \"\"\"使用IQR方法检测异常值\"\"\"\n", "    Q1 = data[column].quantile(0.25)\n", "    Q3 = data[column].quantile(0.75)\n", "    IQR = Q3 - Q1\n", "    lower_bound = Q1 - 1.5 * IQR\n", "    upper_bound = Q3 + 1.5 * IQR\n", "    outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]\n", "    return outliers, lower_bound, upper_bound\n", "\n", "# 检测价格异常值\n", "price_outliers, price_lower, price_upper = detect_outliers_iqr(df, 'price')\n", "print(f\"价格异常值数量: {len(price_outliers)} ({len(price_outliers)/len(df)*100:.2f}%)\")\n", "print(f\"价格正常范围: ${price_lower:,.0f} - ${price_upper:,.0f}\")\n", "\n", "# 检测面积异常值\n", "living_outliers, living_lower, living_upper = detect_outliers_iqr(df, 'sqft_living')\n", "print(f\"居住面积异常值数量: {len(living_outliers)} ({len(living_outliers)/len(df)*100:.2f}%)\")\n"], "id": "e14a649325d6c8cf"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 可视化异常值\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# 价格分布\n", "axes[0,0].hist(df['price'], bins=50, alpha=0.7)\n", "axes[0,0].set_title('房价分布')\n", "axes[0,0].set_xlabel('价格 ($)')\n", "axes[0,0].set_ylabel('频次')\n", "\n", "# 价格箱线图\n", "axes[0,1].boxplot(df['price'])\n", "axes[0,1].set_title('房价箱线图')\n", "axes[0,1].set_ylabel('价格 ($)')\n", "\n", "# 居住面积分布\n", "axes[1,0].hist(df['sqft_living'], bins=50, alpha=0.7)\n", "axes[1,0].set_title('居住面积分布')\n", "axes[1,0].set_xlabel('面积 (sqft)')\n", "axes[1,0].set_ylabel('频次')\n", "\n", "# 居住面积箱线图\n", "axes[1,1].boxplot(df['sqft_living'])\n", "axes[1,1].set_title('居住面积箱线图')\n", "axes[1,1].set_ylabel('面积 (sqft)')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"], "id": "47f87eb472528a5c"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 处理极端异常值（保留合理范围内的数据）\n", "print(\"\\n=== 异常值处理 ===\")\n", "original_size = len(df)\n", "\n", "# 移除极端价格异常值（保留99%的数据）\n", "price_99th = df['price'].quantile(0.99)\n", "price_1st = df['price'].quantile(0.01)\n", "df_clean = df[(df['price'] >= price_1st) & (df['price'] <= price_99th)]\n", "\n", "# 移除极端面积异常值\n", "living_99th = df['sqft_living'].quantile(0.99)\n", "living_1st = df['sqft_living'].quantile(0.01)\n", "df_clean = df_clean[(df_clean['sqft_living'] >= living_1st) & (df_clean['sqft_living'] <= living_99th)]\n", "\n", "print(f\"原始数据量: {original_size}\")\n", "print(f\"清理后数据量: {len(df_clean)}\")\n", "print(f\"移除数据量: {original_size - len(df_clean)} ({(original_size - len(df_clean))/original_size*100:.2f}%)\")\n"], "id": "f89fee877acc99c6"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# ===== 5. 探索性数据分析 (EDA) =====\n", "print(\"\\n=== 探索性数据分析 ===\")\n", "\n", "# 相关性分析\n", "numeric_columns = ['price', 'sqft_lot', 'sqft_living', 'grade', 'lat', 'long', \n", "                  'yr_built', 'house_age', 'price_per_sqft', 'years_since_renovation']\n", "\n", "correlation_matrix = df_clean[numeric_columns].corr()\n", "\n", "plt.figure(figsize=(12, 10))\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, \n", "            square=True, linewidths=0.5)\n", "plt.title('特征相关性热力图')\n", "plt.tight_layout()\n", "plt.show()\n"], "id": "9c0c682ab4e56af9"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 价格与主要特征的关系\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "\n", "# 价格 vs 居住面积\n", "axes[0,0].scatter(df_clean['sqft_living'], df_clean['price'], alpha=0.5)\n", "axes[0,0].set_xlabel('居住面积 (sqft)')\n", "axes[0,0].set_ylabel('价格 ($)')\n", "axes[0,0].set_title('价格 vs 居住面积')\n", "\n", "# 价格 vs 等级\n", "df_clean.boxplot(column='price', by='grade', ax=axes[0,1])\n", "axes[0,1].set_xlabel('建筑等级')\n", "axes[0,1].set_ylabel('价格 ($)')\n", "axes[0,1].set_title('价格 vs 建筑等级')\n", "\n", "# 价格 vs 房屋年龄\n", "axes[0,2].scatter(df_clean['house_age'], df_clean['price'], alpha=0.5)\n", "axes[0,2].set_xlabel('房屋年龄')\n", "axes[0,2].set_ylabel('价格 ($)')\n", "axes[0,2].set_title('价格 vs 房屋年龄')\n", "\n", "# 价格 vs 土地面积\n", "axes[1,0].scatter(df_clean['sqft_lot'], df_clean['price'], alpha=0.5)\n", "axes[1,0].set_xlabel('土地面积 (sqft)')\n", "axes[1,0].set_ylabel('价格 ($)')\n", "axes[1,0].set_title('价格 vs 土地面积')\n", "\n", "# 价格 vs 纬度\n", "axes[1,1].scatter(df_clean['lat'], df_clean['price'], alpha=0.5)\n", "axes[1,1].set_xlabel('纬度')\n", "axes[1,1].set_ylabel('价格 ($)')\n", "axes[1,1].set_title('价格 vs 纬度')\n", "\n", "# 价格 vs 经度\n", "axes[1,2].scatter(df_clean['long'], df_clean['price'], alpha=0.5)\n", "axes[1,2].set_xlabel('经度')\n", "axes[1,2].set_ylabel('价格 ($)')\n", "axes[1,2].set_title('价格 vs 经度')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"], "id": "22942fac55ff1358"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# ===== 6. 特征选择和数据准备 =====\n", "print(\"\\n=== 特征选择和数据准备 ===\")\n", "\n", "# 选择用于建模的特征\n", "feature_columns = ['sqft_lot', 'sqft_living', 'grade', 'lat', 'long', \n", "                  'yr_built', 'house_age', 'is_renovated', 'years_since_renovation',\n", "                  'year', 'month']\n", "\n", "X = df_clean[feature_columns].copy()\n", "y = df_clean['price'].copy()\n", "\n", "print(f\"特征数量: {X.shape[1]}\")\n", "print(f\"样本数量: {X.shape[0]}\")\n", "print(\"选择的特征:\", feature_columns)\n"], "id": "b13b0c1332727b7e"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 数据标准化\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "\n", "# 划分训练集和测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "print(f\"训练集大小: {X_train.shape}\")\n", "print(f\"测试集大小: {X_test.shape}\")\n", "\n", "# 标准化特征\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# MinMax标准化（用于某些算法）\n", "minmax_scaler = MinMaxScaler()\n", "X_train_minmax = minmax_scaler.fit_transform(X_train)\n", "X_test_minmax = minmax_scaler.transform(X_test)\n", "\n", "print(\"数据标准化完成\")\n"], "id": "8ac8087e29e9c309"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# ===== 7. 机器学习模型训练和评估 =====\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "from sklearn.linear_model import LinearRegression, Ridge, Lasso\n", "from sklearn.svm import SVR\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "import joblib\n", "\n", "print(\"\\n=== 机器学习模型训练 ===\")\n", "\n", "# 定义评估函数\n", "def evaluate_model(y_true, y_pred, model_name):\n", "    \"\"\"评估模型性能\"\"\"\n", "    mse = mean_squared_error(y_true, y_pred)\n", "    rmse = np.sqrt(mse)\n", "    mae = mean_absolute_error(y_true, y_pred)\n", "    r2 = r2_score(y_true, y_pred)\n", "    \n", "    print(f\"\\n{model_name} 模型评估:\")\n", "    print(f\"均方误差 (MSE): {mse:,.2f}\")\n", "    print(f\"均方根误差 (RMSE): {rmse:,.2f}\")\n", "    print(f\"平均绝对误差 (MAE): {mae:,.2f}\")\n", "    print(f\"决定系数 (R²): {r2:.4f}\")\n", "    \n", "    return {'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'R2': r2}\n", "\n", "# 存储模型结果\n", "model_results = {}\n"], "id": "39f3d65004a64997"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 1. 线性回归\n", "print(\"训练线性回归模型...\")\n", "lr_model = LinearRegression()\n", "lr_model.fit(X_train_scaled, y_train)\n", "lr_pred = lr_model.predict(X_test_scaled)\n", "model_results['Linear Regression'] = evaluate_model(y_test, lr_pred, \"线性回归\")\n"], "id": "20327fcb560a0b67"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 2. <PERSON>回归\n", "print(\"训练Ridge回归模型...\")\n", "ridge_model = Ridge(alpha=1.0, random_state=42)\n", "ridge_model.fit(X_train_scaled, y_train)\n", "ridge_pred = ridge_model.predict(X_test_scaled)\n", "model_results['Ridge'] = evaluate_model(y_test, ridge_pred, \"Ridge回归\")\n"], "id": "47c28315ccf8265f"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 3. <PERSON><PERSON>回归\n", "print(\"训练Lasso回归模型...\")\n", "lasso_model = Lasso(alpha=1000.0, random_state=42)\n", "lasso_model.fit(X_train_scaled, y_train)\n", "lasso_pred = lasso_model.predict(X_test_scaled)\n", "model_results['Lasso'] = evaluate_model(y_test, lasso_pred, \"Lasso回归\")\n"], "id": "e5cb2f8d8711371c"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 4. 随机森林\n", "print(\"训练随机森林模型...\")\n", "rf_model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)\n", "rf_model.fit(X_train, y_train)\n", "rf_pred = rf_model.predict(X_test)\n", "model_results['Random Forest'] = evaluate_model(y_test, rf_pred, \"随机森林\")\n"], "id": "f788ca24bae6862d"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 5. 梯度提升\n", "print(\"训练梯度提升模型...\")\n", "gb_model = GradientBoostingRegressor(n_estimators=100, random_state=42)\n", "gb_model.fit(X_train, y_train)\n", "gb_pred = gb_model.predict(X_test)\n", "model_results['Gradient Boosting'] = evaluate_model(y_test, gb_pred, \"梯度提升\")\n"], "id": "88964deadb2d825f"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 6. 支持向量回归\n", "print(\"训练支持向量回归模型...\")\n", "svr_model = SVR(kernel='rbf', C=1000, gamma='scale')\n", "svr_model.fit(X_train_scaled, y_train)\n", "svr_pred = svr_model.predict(X_test_scaled)\n", "model_results['SVR'] = evaluate_model(y_test, svr_pred, \"支持向量回归\")\n"], "id": "a0f33f614011371f"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# ===== 8. 模型比较和可视化 =====\n", "print(\"\\n=== 模型性能比较 ===\")\n", "\n", "# 创建比较表格\n", "comparison_df = pd.DataFrame(model_results).T\n", "comparison_df = comparison_df.round(2)\n", "print(comparison_df)\n", "\n", "# 找出最佳模型\n", "best_model_name = comparison_df['R2'].idxmax()\n", "print(f\"\\n最佳模型: {best_model_name} (R² = {comparison_df.loc[best_model_name, 'R2']:.4f})\")\n"], "id": "e953165f2154ced9"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 可视化模型比较\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# R²比较\n", "axes[0,0].bar(comparison_df.index, comparison_df['R2'])\n", "axes[0,0].set_title('模型R²比较')\n", "axes[0,0].set_ylabel('R²')\n", "axes[0,0].tick_params(axis='x', rotation=45)\n", "\n", "# RMSE比较\n", "axes[0,1].bar(comparison_df.index, comparison_df['RMSE'])\n", "axes[0,1].set_title('模型RMSE比较')\n", "axes[0,1].set_ylabel('RMSE')\n", "axes[0,1].tick_params(axis='x', rotation=45)\n", "\n", "# MAE比较\n", "axes[1,0].bar(comparison_df.index, comparison_df['MAE'])\n", "axes[1,0].set_title('模型MAE比较')\n", "axes[1,0].set_ylabel('MAE')\n", "axes[1,0].tick_params(axis='x', rotation=45)\n", "\n", "# MSE比较\n", "axes[1,1].bar(comparison_df.index, comparison_df['MSE'])\n", "axes[1,1].set_title('模型MSE比较')\n", "axes[1,1].set_ylabel('MSE')\n", "axes[1,1].tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()\n"], "id": "4873b7bebd26cb91"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 预测值 vs 实际值可视化\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "predictions = [lr_pred, ridge_pred, lasso_pred, rf_pred, gb_pred, svr_pred]\n", "model_names = ['Linear Regression', '<PERSON>', '<PERSON><PERSON>', 'Random Forest', 'Gradient Boosting', 'SVR']\n", "\n", "for i, (pred, name) in enumerate(zip(predictions, model_names)):\n", "    row = i // 3\n", "    col = i % 3\n", "\n", "    axes[row, col].scatter(y_test, pred, alpha=0.5)\n", "    axes[row, col].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "    axes[row, col].set_xlabel('实际价格')\n", "    axes[row, col].set_ylabel('预测价格')\n", "    axes[row, col].set_title(f'{name}')\n", "\n", "    # 添加R²值\n", "    r2 = model_results[name]['R2']\n", "    axes[row, col].text(0.05, 0.95, f'R² = {r2:.3f}', transform=axes[row, col].transAxes,\n", "                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "\n", "plt.tight_layout()\n", "plt.show()\n"], "id": "c28e37088dcc3dd4"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# ===== 9. 特征重要性分析 =====\n", "print(\"\\n=== 特征重要性分析 ===\")\n", "\n", "# 随机森林特征重要性\n", "rf_importance = pd.DataFrame({\n", "    '特征': feature_columns,\n", "    '重要性': rf_model.feature_importances_\n", "}).sort_values('重要性', ascending=False)\n", "\n", "print(\"随机森林特征重要性:\")\n", "print(rf_importance)\n", "\n", "# 梯度提升特征重要性\n", "gb_importance = pd.DataFrame({\n", "    '特征': feature_columns,\n", "    '重要性': gb_model.feature_importances_\n", "}).sort_values('重要性', ascending=False)\n", "\n", "print(\"\\n梯度提升特征重要性:\")\n", "print(gb_importance)\n"], "id": "12ed6ed976a81717"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# 可视化特征重要性\n", "fig, axes = plt.subplots(1, 2, figsize=(16, 6))\n", "\n", "# 随机森林特征重要性\n", "axes[0].barh(rf_importance['特征'], rf_importance['重要性'])\n", "axes[0].set_title('随机森林 - 特征重要性')\n", "axes[0].set_xlabel('重要性')\n", "\n", "# 梯度提升特征重要性\n", "axes[1].barh(gb_importance['特征'], gb_importance['重要性'])\n", "axes[1].set_title('梯度提升 - 特征重要性')\n", "axes[1].set_xlabel('重要性')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"], "id": "a6fb7cf5a5803e0e"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# ===== 10. 残差分析 =====\n", "print(\"\\n=== 残差分析 ===\")\n", "\n", "# 选择最佳模型进行残差分析\n", "if best_model_name == 'Random Forest':\n", "    best_pred = rf_pred\n", "    best_model = rf_model\n", "elif best_model_name == 'Gradient Boosting':\n", "    best_pred = gb_pred\n", "    best_model = gb_model\n", "elif best_model_name == 'Linear Regression':\n", "    best_pred = lr_pred\n", "    best_model = lr_model\n", "elif best_model_name == 'Ridge':\n", "    best_pred = ridge_pred\n", "    best_model = ridge_model\n", "elif best_model_name == '<PERSON><PERSON>':\n", "    best_pred = lasso_pred\n", "    best_model = lasso_model\n", "else:\n", "    best_pred = svr_pred\n", "    best_model = svr_model\n", "\n", "residuals = y_test - best_pred\n", "\n", "# 残差分析图\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# 残差 vs 预测值\n", "axes[0,0].scatter(best_pred, residuals, alpha=0.5)\n", "axes[0,0].axhline(y=0, color='r', linestyle='--')\n", "axes[0,0].set_xlabel('预测值')\n", "axes[0,0].set_ylabel('残差')\n", "axes[0,0].set_title('残差 vs 预测值')\n", "\n", "# 残差直方图\n", "axes[0,1].hist(residuals, bins=50, alpha=0.7)\n", "axes[0,1].set_xlabel('残差')\n", "axes[0,1].set_ylabel('频次')\n", "axes[0,1].set_title('残差分布')\n", "\n", "# Q-Q图\n", "from scipy import stats\n", "stats.probplot(residuals, dist=\"norm\", plot=axes[1,0])\n", "axes[1,0].set_title('残差Q-Q图')\n", "\n", "# 残差 vs 实际值\n", "axes[1,1].scatter(y_test, residuals, alpha=0.5)\n", "axes[1,1].axhline(y=0, color='r', linestyle='--')\n", "axes[1,1].set_xlabel('实际值')\n", "axes[1,1].set_ylabel('残差')\n", "axes[1,1].set_title('残差 vs 实际值')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"], "id": "46191aaa3eec8ab4"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# ===== 11. 模型保存 =====\n", "print(\"\\n=== 模型保存 ===\")\n", "\n", "# 创建模型目录\n", "model_dir = 'model'\n", "if not os.path.exists(model_dir):\n", "    os.makedirs(model_dir)\n", "\n", "# 保存最佳模型\n", "best_model_path = os.path.join(model_dir, f'best_model_{best_model_name.lower().replace(\" \", \"_\")}.pkl')\n", "joblib.dump(best_model, best_model_path)\n", "print(f\"最佳模型已保存到: {best_model_path}\")\n", "\n", "# 保存标准化器\n", "scaler_path = os.path.join(model_dir, 'scaler.pkl')\n", "joblib.dump(scaler, scaler_path)\n", "print(f\"标准化器已保存到: {scaler_path}\")\n", "\n", "# 保存特征列名\n", "feature_path = os.path.join(model_dir, 'feature_columns.pkl')\n", "joblib.dump(feature_columns, feature_path)\n", "print(f\"特征列名已保存到: {feature_path}\")\n", "\n", "# 保存模型比较结果\n", "results_path = os.path.join(model_dir, 'model_comparison.csv')\n", "comparison_df.to_csv(results_path)\n", "print(f\"模型比较结果已保存到: {results_path}\")\n"], "id": "bbce10f6593072d2"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# ===== 12. 业务洞察和总结 =====\n", "print(\"\\n=== 业务洞察和总结 ===\")\n", "\n", "print(\"1. 数据质量:\")\n", "print(f\"   - 原始数据: {original_size:,} 条记录\")\n", "print(f\"   - 清理后数据: {len(df_clean):,} 条记录\")\n", "print(f\"   - 数据清理率: {(original_size - len(df_clean))/original_size*100:.1f}%\")\n", "\n", "print(\"\\n2. 模型性能:\")\n", "print(f\"   - 最佳模型: {best_model_name}\")\n", "print(f\"   - R²得分: {comparison_df.loc[best_model_name, 'R2']:.4f}\")\n", "print(f\"   - RMSE: ${comparison_df.loc[best_model_name, 'RMSE']:,.0f}\")\n", "print(f\"   - MAE: ${comparison_df.loc[best_model_name, 'MAE']:,.0f}\")\n", "\n", "print(\"\\n3. 关键特征 (基于随机森林):\")\n", "for i, row in rf_importance.head(5).iterrows():\n", "    print(f\"   - {row['特征']}: {row['重要性']:.3f}\")\n", "\n", "print(\"\\n4. 业务建议:\")\n", "print(\"   - 居住面积是影响房价的最重要因素\")\n", "print(\"   - 建筑等级和地理位置也显著影响房价\")\n", "print(\"   - 模型可用于房价估算，但需要考虑市场变化\")\n", "print(\"   - 建议定期更新模型以适应市场变化\")\n"], "id": "ac0ce4e7f6a8a44f"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "dfbccfb1464ea1d0"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}