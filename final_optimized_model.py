#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

#%%
print("=== 最终优化模型 ===")

# 加载和预处理数据
df = pd.read_csv('data.csv')
print(f"原始数据形状: {df.shape}")

# 数据预处理
df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')
df['year'] = df['date'].dt.year
df['month'] = df['date'].dt.month
df['day_of_year'] = df['date'].dt.dayofyear

df['is_renovated'] = (df['yr_renovated'] > 0).astype(int)
df['years_since_renovation'] = np.where(df['yr_renovated'] > 0, 
                                       df['year'] - df['yr_renovated'], 
                                       df['year'] - df['yr_built'])

df['house_age'] = df['year'] - df['yr_built']
df['price_per_sqft'] = df['price'] / df['sqft_living']

# 数据清理
price_99_5th = df['price'].quantile(0.995)
price_0_5th = df['price'].quantile(0.005)
df_clean = df[(df['price'] >= price_0_5th) & (df['price'] <= price_99_5th)]

living_99_5th = df['sqft_living'].quantile(0.995)
living_0_5th = df['sqft_living'].quantile(0.005)
df_clean = df_clean[(df_clean['sqft_living'] >= living_0_5th) & (df_clean['sqft_living'] <= living_99_5th)]

df_clean = df_clean[df_clean['price'] > 0]
df_clean = df_clean[df_clean['sqft_living'] > 0]
df_clean = df_clean[df_clean['sqft_lot'] > 0]

print(f"清理后数据形状: {df_clean.shape}")

#%%
# 特征选择（基于测试结果，保持原始特征）
feature_columns = ['sqft_lot', 'sqft_living', 'grade', 'lat', 'long', 
                  'yr_built', 'house_age', 'is_renovated', 'years_since_renovation',
                  'year', 'month']

X = df_clean[feature_columns].copy()
y = df_clean['price'].copy()

print(f"特征数量: {X.shape[1]}")
print(f"样本数量: {X.shape[0]}")
print("选择的特征:", feature_columns)

# 数据分割
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 标准化特征（用于线性模型）
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

print(f"训练集大小: {X_train.shape}")
print(f"测试集大小: {X_test.shape}")

#%%
# 评估函数
def evaluate_model(y_true, y_pred, model_name):
    """评估模型性能"""
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
    
    print(f"\n{model_name} 模型评估:")
    print(f"  均方根误差 (RMSE): ${rmse:,.0f}")
    print(f"  平均绝对误差 (MAE): ${mae:,.0f}")
    print(f"  决定系数 (R²): {r2:.4f}")
    print(f"  平均绝对百分比误差 (MAPE): {mape:.2f}%")
    
    return {'RMSE': rmse, 'MAE': mae, 'R2': r2, 'MAPE': mape}

#%%
# 模型训练和评估
print("\n=== 模型训练和评估 ===")
model_results = {}

# 1. 线性回归（对数变换）
print("训练线性回归模型（对数变换）...")
y_train_log = np.log(y_train)
lr_model = LinearRegression()
lr_model.fit(X_train_scaled, y_train_log)
lr_pred_log = lr_model.predict(X_test_scaled)
lr_pred = np.exp(lr_pred_log)
model_results['线性回归'] = evaluate_model(y_test, lr_pred, "线性回归（对数变换）")

# 2. Ridge回归（对数变换）
print("训练Ridge回归模型...")
ridge_model = Ridge(alpha=1.0)
ridge_model.fit(X_train_scaled, y_train_log)
ridge_pred_log = ridge_model.predict(X_test_scaled)
ridge_pred = np.exp(ridge_pred_log)
model_results['Ridge回归'] = evaluate_model(y_test, ridge_pred, "Ridge回归（对数变换）")

# 3. 随机森林（最佳模型）
print("训练随机森林模型...")
rf_model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
rf_model.fit(X_train, y_train)
rf_pred = rf_model.predict(X_test)
model_results['随机森林'] = evaluate_model(y_test, rf_pred, "随机森林")

# 4. 优化的随机森林
print("训练优化随机森林模型...")
rf_optimized = RandomForestRegressor(
    n_estimators=200, 
    max_depth=20, 
    min_samples_split=5,
    min_samples_leaf=2,
    random_state=42, 
    n_jobs=-1
)
rf_optimized.fit(X_train, y_train)
rf_opt_pred = rf_optimized.predict(X_test)
model_results['优化随机森林'] = evaluate_model(y_test, rf_opt_pred, "优化随机森林")

# 5. 梯度提升
print("训练梯度提升模型...")
gb_model = GradientBoostingRegressor(n_estimators=100, learning_rate=0.1, random_state=42)
gb_model.fit(X_train, y_train)
gb_pred = gb_model.predict(X_test)
model_results['梯度提升'] = evaluate_model(y_test, gb_pred, "梯度提升")

#%%
# 结果比较
print("\n=== 模型性能比较 ===")
comparison_df = pd.DataFrame(model_results).T
comparison_df = comparison_df.round(2)
print(comparison_df)

best_model_name = comparison_df['RMSE'].idxmin()
print(f"\n🏆 最佳模型: {best_model_name}")
print(f"   RMSE: ${comparison_df.loc[best_model_name, 'RMSE']:,.0f}")
print(f"   R²: {comparison_df.loc[best_model_name, 'R2']:.4f}")
print(f"   MAPE: {comparison_df.loc[best_model_name, 'MAPE']:.2f}%")

#%%
# 可视化结果
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# RMSE比较
axes[0,0].bar(comparison_df.index, comparison_df['RMSE'])
axes[0,0].set_title('RMSE比较')
axes[0,0].set_ylabel('RMSE ($)')
axes[0,0].tick_params(axis='x', rotation=45)

# R²比较
axes[0,1].bar(comparison_df.index, comparison_df['R2'])
axes[0,1].set_title('R²比较')
axes[0,1].set_ylabel('R²')
axes[0,1].tick_params(axis='x', rotation=45)

# MAE比较
axes[1,0].bar(comparison_df.index, comparison_df['MAE'])
axes[1,0].set_title('MAE比较')
axes[1,0].set_ylabel('MAE ($)')
axes[1,0].tick_params(axis='x', rotation=45)

# MAPE比较
axes[1,1].bar(comparison_df.index, comparison_df['MAPE'])
axes[1,1].set_title('MAPE比较')
axes[1,1].set_ylabel('MAPE (%)')
axes[1,1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

#%%
# 特征重要性分析
print("\n=== 特征重要性分析 ===")

# 随机森林特征重要性
feature_importance = pd.DataFrame({
    '特征': feature_columns,
    '重要性': rf_model.feature_importances_
}).sort_values('重要性', ascending=False)

print("随机森林特征重要性:")
for _, row in feature_importance.iterrows():
    print(f"  {row['特征']}: {row['重要性']:.4f}")

# 可视化特征重要性
plt.figure(figsize=(10, 6))
plt.barh(range(len(feature_importance)), feature_importance['重要性'])
plt.yticks(range(len(feature_importance)), feature_importance['特征'])
plt.xlabel('重要性')
plt.title('特征重要性（随机森林）')
plt.gca().invert_yaxis()
plt.tight_layout()
plt.show()

#%%
# 预测值 vs 实际值可视化（最佳模型）
plt.figure(figsize=(10, 8))

if best_model_name == '随机森林':
    best_pred = rf_pred
elif best_model_name == '优化随机森林':
    best_pred = rf_opt_pred
elif best_model_name == '梯度提升':
    best_pred = gb_pred
else:
    best_pred = lr_pred

plt.scatter(y_test, best_pred, alpha=0.5)
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
plt.xlabel('实际价格 ($)')
plt.ylabel('预测价格 ($)')
plt.title(f'{best_model_name} - 预测值 vs 实际值')

# 添加统计信息
r2 = comparison_df.loc[best_model_name, 'R2']
rmse = comparison_df.loc[best_model_name, 'RMSE']
plt.text(0.05, 0.95, f'R² = {r2:.4f}\nRMSE = ${rmse:,.0f}', 
         transform=plt.gca().transAxes, 
         bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
         verticalalignment='top')

plt.tight_layout()
plt.show()

#%%
# 业务洞察总结
print("\n=== 业务洞察总结 ===")
print("1. 模型性能:")
print(f"   - 最佳模型: {best_model_name}")
print(f"   - 预测精度: R² = {comparison_df.loc[best_model_name, 'R2']:.4f}")
print(f"   - 平均误差: RMSE = ${comparison_df.loc[best_model_name, 'RMSE']:,.0f}")
print(f"   - 相对误差: MAPE = {comparison_df.loc[best_model_name, 'MAPE']:.2f}%")

print("\n2. 关键发现:")
print("   - 随机森林比线性回归效果显著更好")
print("   - 树模型能更好地捕获非线性关系")
print("   - 特征工程对树模型的影响相对较小")

print("\n3. 特征重要性:")
top_3_features = feature_importance.head(3)
for _, row in top_3_features.iterrows():
    print(f"   - {row['特征']}: {row['重要性']:.4f}")

print("\n4. 业务建议:")
print("   - 推荐使用随机森林作为生产模型")
print("   - 重点关注居住面积、等级和位置信息")
print("   - 模型可用于房价估算，误差在可接受范围内")

print("\n最终优化完成！")
