#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示为方块的问题

#%%
# 加载数据
df = pd.read_csv('data.csv')
print(f"数据形状: {df.shape}")

#%%
# 改进的异常值检测函数
def detect_outliers_iqr(data, column):
    """使用改进的IQR方法检测异常值"""
    Q1 = data[column].quantile(0.25)
    Q3 = data[column].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    
    # 对于价格数据，下界不能为负值
    if column == 'price':
        lower_bound = max(0, lower_bound)
    
    outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]
    return outliers, lower_bound, upper_bound

#%%
# 测试价格异常值检测
print("=== 价格异常值检测测试 ===")
price_outliers, price_lower, price_upper = detect_outliers_iqr(df, 'price')
print(f"价格异常值数量: {len(price_outliers)} ({len(price_outliers)/len(df)*100:.2f}%)")
print(f"价格正常范围: ${price_lower:,.0f} - ${price_upper:,.0f}")

print(f"\n价格统计信息:")
print(f"最小值: ${df['price'].min():,.0f}")
print(f"最大值: ${df['price'].max():,.0f}")
print(f"中位数: ${df['price'].median():,.0f}")
print(f"平均值: ${df['price'].mean():,.0f}")
print(f"Q1 (25%): ${df['price'].quantile(0.25):,.0f}")
print(f"Q3 (75%): ${df['price'].quantile(0.75):,.0f}")

#%%
# 使用百分位数方法
print("\n=== 百分位数方法 ===")
price_99_5 = df['price'].quantile(0.995)
price_0_5 = df['price'].quantile(0.005)
price_99 = df['price'].quantile(0.99)
price_1 = df['price'].quantile(0.01)

print(f"0.5% 分位数: ${price_0_5:,.0f}")
print(f"1% 分位数: ${price_1:,.0f}")
print(f"99% 分位数: ${price_99:,.0f}")
print(f"99.5% 分位数: ${price_99_5:,.0f}")

extreme_outliers_0_5 = df[(df['price'] < price_0_5) | (df['price'] > price_99_5)]
extreme_outliers_1 = df[(df['price'] < price_1) | (df['price'] > price_99)]

print(f"\n极端异常值 (0.5%-99.5%): {len(extreme_outliers_0_5)} ({len(extreme_outliers_0_5)/len(df)*100:.2f}%)")
print(f"异常值 (1%-99%): {len(extreme_outliers_1)} ({len(extreme_outliers_1)/len(df)*100:.2f}%)")

#%%
# 可视化价格分布
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# 原始价格分布
axes[0,0].hist(df['price'], bins=100, alpha=0.7)
axes[0,0].set_title('原始价格分布')
axes[0,0].set_xlabel('价格 ($)')
axes[0,0].set_ylabel('频次')

# 价格箱线图
axes[0,1].boxplot([df['price']])
axes[0,1].set_title('价格箱线图')
axes[0,1].set_ylabel('价格 ($)')
axes[0,1].set_xticklabels(['价格'])

# 对数价格分布
axes[1,0].hist(np.log(df['price']), bins=100, alpha=0.7)
axes[1,0].set_title('对数价格分布')
axes[1,0].set_xlabel('log(价格)')
axes[1,0].set_ylabel('频次')

# 价格散点图（按索引）
axes[1,1].scatter(range(len(df)), df['price'], alpha=0.5, s=1)
axes[1,1].set_title('价格散点图')
axes[1,1].set_xlabel('数据索引')
axes[1,1].set_ylabel('价格 ($)')

plt.tight_layout()
plt.show()

print("异常值检测测试完成！")
