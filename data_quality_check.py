#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示为方块的问题

#%%
# 加载数据并进行全面检查
print("=== 数据质量全面检查 ===")
df = pd.read_csv('data.csv')
print(f"数据形状: {df.shape}")
print(f"列名: {df.columns.tolist()}")

#%%
# 1. 检查数据类型
print("\n=== 数据类型检查 ===")
print(df.dtypes)
print("\n数据基本信息:")
print(df.info())

#%%
# 2. 检查每列的统计信息
print("\n=== 各列统计信息 ===")
print(df.describe())

#%%
# 3. 检查缺失值和异常值
print("\n=== 缺失值检查 ===")
print("缺失值数量:")
print(df.isnull().sum())

print("\n=== 负值检查 ===")
numeric_columns = ['price', 'sqft_lot', 'sqft_living', 'grade', 'lat', 'long', 'yr_built', 'yr_renovated']

for col in numeric_columns:
    if col in df.columns:
        negative_count = (df[col] < 0).sum()
        if negative_count > 0:
            print(f"{col}: {negative_count} 个负值")
            print(f"  负值范围: {df[df[col] < 0][col].min()} 到 {df[df[col] < 0][col].max()}")
        else:
            print(f"{col}: 无负值")

#%%
# 4. 检查零值
print("\n=== 零值检查 ===")
for col in numeric_columns:
    if col in df.columns:
        zero_count = (df[col] == 0).sum()
        print(f"{col}: {zero_count} 个零值 ({zero_count/len(df)*100:.2f}%)")

#%%
# 5. 检查每列的值范围和分布
print("\n=== 各列值范围检查 ===")
for col in numeric_columns:
    if col in df.columns:
        print(f"\n{col}:")
        print(f"  最小值: {df[col].min()}")
        print(f"  最大值: {df[col].max()}")
        print(f"  中位数: {df[col].median()}")
        print(f"  平均值: {df[col].mean():.2f}")
        print(f"  标准差: {df[col].std():.2f}")
        
        # 检查是否有异常大或异常小的值
        q1 = df[col].quantile(0.25)
        q3 = df[col].quantile(0.75)
        iqr = q3 - q1
        print(f"  Q1: {q1}, Q3: {q3}, IQR: {iqr}")

#%%
# 6. 检查特定列的合理性
print("\n=== 数据合理性检查 ===")

# 检查价格
print("价格检查:")
price_issues = []
if (df['price'] <= 0).any():
    price_issues.append(f"有 {(df['price'] <= 0).sum()} 个非正价格")
if (df['price'] > 50000000).any():  # 5000万美元以上可能不合理
    price_issues.append(f"有 {(df['price'] > 50000000).sum()} 个超高价格(>5000万)")

if price_issues:
    for issue in price_issues:
        print(f"  ⚠️ {issue}")
else:
    print("  ✅ 价格数据看起来合理")

# 检查面积
print("\n面积检查:")
area_issues = []
if (df['sqft_living'] <= 0).any():
    area_issues.append(f"有 {(df['sqft_living'] <= 0).sum()} 个非正居住面积")
if (df['sqft_lot'] <= 0).any():
    area_issues.append(f"有 {(df['sqft_lot'] <= 0).sum()} 个非正土地面积")
if (df['sqft_living'] > 50000).any():  # 5万平方英尺以上可能不合理
    area_issues.append(f"有 {(df['sqft_living'] > 50000).sum()} 个超大居住面积(>50000 sqft)")

if area_issues:
    for issue in area_issues:
        print(f"  ⚠️ {issue}")
else:
    print("  ✅ 面积数据看起来合理")

# 检查等级
print("\n等级检查:")
if df['grade'].min() < 1 or df['grade'].max() > 13:
    print(f"  ⚠️ 等级超出1-13范围: {df['grade'].min()} - {df['grade'].max()}")
else:
    print("  ✅ 等级在合理范围内(1-13)")

# 检查年份
print("\n年份检查:")
current_year = 2024
if (df['yr_built'] < 1800).any() or (df['yr_built'] > current_year).any():
    print(f"  ⚠️ 建筑年份异常: {df['yr_built'].min()} - {df['yr_built'].max()}")
else:
    print("  ✅ 建筑年份合理")

if (df['yr_renovated'] > current_year).any():
    print(f"  ⚠️ 翻新年份超过当前年份")
elif (df['yr_renovated'] > 0).any() and (df['yr_renovated'] < 1800).any():
    print(f"  ⚠️ 翻新年份过早")
else:
    print("  ✅ 翻新年份合理")

#%%
# 7. 检查数据的前几行和后几行
print("\n=== 数据样本检查 ===")
print("前5行:")
print(df.head())
print("\n后5行:")
print(df.tail())

#%%
# 8. 检查是否有重复行
print(f"\n=== 重复数据检查 ===")
duplicates = df.duplicated().sum()
print(f"完全重复的行数: {duplicates}")

# 检查关键字段组合的重复
key_duplicates = df.duplicated(subset=['date', 'price', 'sqft_living', 'lat', 'long']).sum()
print(f"关键字段组合重复的行数: {key_duplicates}")

#%%
# 9. 可视化数据分布
print("\n=== 生成数据分布图 ===")
fig, axes = plt.subplots(3, 3, figsize=(18, 15))

# 价格分布
axes[0,0].hist(df['price'], bins=100, alpha=0.7)
axes[0,0].set_title('价格分布')
axes[0,0].set_xlabel('价格 ($)')

# 居住面积分布
axes[0,1].hist(df['sqft_living'], bins=100, alpha=0.7)
axes[0,1].set_title('居住面积分布')
axes[0,1].set_xlabel('面积 (sqft)')

# 土地面积分布
axes[0,2].hist(df['sqft_lot'], bins=100, alpha=0.7)
axes[0,2].set_title('土地面积分布')
axes[0,2].set_xlabel('面积 (sqft)')

# 等级分布
axes[1,0].hist(df['grade'], bins=range(1, 15), alpha=0.7)
axes[1,0].set_title('建筑等级分布')
axes[1,0].set_xlabel('等级')

# 建筑年份分布
axes[1,1].hist(df['yr_built'], bins=50, alpha=0.7)
axes[1,1].set_title('建筑年份分布')
axes[1,1].set_xlabel('年份')

# 纬度分布
axes[1,2].hist(df['lat'], bins=50, alpha=0.7)
axes[1,2].set_title('纬度分布')
axes[1,2].set_xlabel('纬度')

# 经度分布
axes[2,0].hist(df['long'], bins=50, alpha=0.7)
axes[2,0].set_title('经度分布')
axes[2,0].set_xlabel('经度')

# 价格vs面积散点图
axes[2,1].scatter(df['sqft_living'], df['price'], alpha=0.5, s=1)
axes[2,1].set_title('价格 vs 居住面积')
axes[2,1].set_xlabel('居住面积 (sqft)')
axes[2,1].set_ylabel('价格 ($)')

# 翻新年份分布（排除0）
renovated_data = df[df['yr_renovated'] > 0]['yr_renovated']
if len(renovated_data) > 0:
    axes[2,2].hist(renovated_data, bins=30, alpha=0.7)
    axes[2,2].set_title('翻新年份分布')
    axes[2,2].set_xlabel('年份')
else:
    axes[2,2].text(0.5, 0.5, '无翻新数据', ha='center', va='center', transform=axes[2,2].transAxes)
    axes[2,2].set_title('翻新年份分布')

plt.tight_layout()
plt.show()

print("\n数据质量检查完成！请查看上述结果并告诉我发现的问题。")
