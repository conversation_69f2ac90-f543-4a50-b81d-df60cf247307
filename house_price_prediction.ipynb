{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2025-08-02T06:28:37.321494Z", "start_time": "2025-08-02T06:28:37.308224Z"}}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import os\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置支持中文的字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体\n", "plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示为方块的问题\n"]}, {"cell_type": "code", "execution_count": 2, "id": "e6d91f525bec330b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 数据加载 ===\n", "数据集形状: (21613, 9)\n", "数据集列名: ['date', 'price', 'sqft_lot', 'sqft_living', 'grade', 'lat', 'long', 'yr_built', 'yr_renovated']\n", "\n", "数据集前5行:\n", "       date   price  sqft_lot  sqft_living  grade      lat     long  yr_built  \\\n", "0  20141013  221900      5650         1180      7  47.5112 -122.257      1955   \n", "1  20141209  538000      7242         2570      7  47.7210 -122.319      1951   \n", "2  20150225  180000     10000          770      6  47.7379 -122.233      1933   \n", "3  20141209  604000      5000         1960      7  47.5208 -122.393      1965   \n", "4  20150218  510000      8080         1680      8  47.6168 -122.045      1987   \n", "\n", "   yr_renovated  \n", "0             0  \n", "1          1991  \n", "2             0  \n", "3             0  \n", "4             0  \n"]}], "source": ["# ===== 1. 数据加载和初步探索 =====\n", "print(\"=== 数据加载 ===\")\n", "df = pd.read_csv('data.csv')\n", "print(f\"数据集形状: {df.shape}\")\n", "print(f\"数据集列名: {df.columns.tolist()}\")\n", "print(\"\\n数据集前5行:\")\n", "print(df.head())\n"]}, {"cell_type": "code", "execution_count": 3, "id": "259e76ed6aba121", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 数据基本信息 ===\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 21613 entries, 0 to 21612\n", "Data columns (total 9 columns):\n", " #   Column        Non-Null Count  Dtype  \n", "---  ------        --------------  -----  \n", " 0   date          21613 non-null  int64  \n", " 1   price         21613 non-null  int64  \n", " 2   sqft_lot      21613 non-null  int64  \n", " 3   sqft_living   21613 non-null  int64  \n", " 4   grade         21613 non-null  int64  \n", " 5   lat           21613 non-null  float64\n", " 6   long          21613 non-null  float64\n", " 7   yr_built      21613 non-null  int64  \n", " 8   yr_renovated  21613 non-null  int64  \n", "dtypes: float64(2), int64(7)\n", "memory usage: 1.5 MB\n", "None\n", "\n", "数据统计描述:\n", "               date         price      sqft_lot   sqft_living         grade  \\\n", "count  2.161300e+04  2.161300e+04  2.161300e+04  21613.000000  21613.000000   \n", "mean   2.014390e+07  5.400881e+05  1.510697e+04   2079.899736      7.656873   \n", "std    4.436582e+03  3.671272e+05  4.142051e+04    918.440897      1.175459   \n", "min    2.014050e+07  7.500000e+04  5.200000e+02    290.000000      1.000000   \n", "25%    2.014072e+07  3.219500e+05  5.040000e+03   1427.000000      7.000000   \n", "50%    2.014102e+07  4.500000e+05  7.618000e+03   1910.000000      7.000000   \n", "75%    2.015022e+07  6.450000e+05  1.068800e+04   2550.000000      8.000000   \n", "max    2.015053e+07  7.700000e+06  1.651359e+06  13540.000000     13.000000   \n", "\n", "                lat          long      yr_built  yr_renovated  \n", "count  21613.000000  21613.000000  21613.000000  21613.000000  \n", "mean      47.560053   -122.213896   1971.005136     84.402258  \n", "std        0.138564      0.140828     29.373411    401.679240  \n", "min       47.155900   -122.519000   1900.000000      0.000000  \n", "25%       47.471000   -122.328000   1951.000000      0.000000  \n", "50%       47.571800   -122.230000   1975.000000      0.000000  \n", "75%       47.678000   -122.125000   1997.000000      0.000000  \n", "max       47.777600   -121.315000   2015.000000   2015.000000  \n"]}], "source": ["# 数据基本信息\n", "print(\"\\n=== 数据基本信息 ===\")\n", "print(df.info())\n", "print(\"\\n数据统计描述:\")\n", "print(df.describe())\n"]}, {"cell_type": "code", "execution_count": 4, "id": "fb49a91c77b4f35d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 缺失值检查 ===\n", "各列缺失值数量:\n", "date            0\n", "price           0\n", "sqft_lot        0\n", "sqft_living     0\n", "grade           0\n", "lat             0\n", "long            0\n", "yr_built        0\n", "yr_renovated    0\n", "dtype: int64\n", "\n", "总缺失值数量: 0\n", "缺失值比例: 0.00%\n"]}], "source": ["# ===== 2. 数据质量检查 =====\n", "print(\"\\n=== 缺失值检查 ===\")\n", "missing_values = df.isnull().sum()\n", "print(\"各列缺失值数量:\")\n", "print(missing_values)\n", "print(f\"\\n总缺失值数量: {missing_values.sum()}\")\n", "print(f\"缺失值比例: {(missing_values.sum() / (df.shape[0] * df.shape[1])) * 100:.2f}%\")\n"]}, {"cell_type": "code", "execution_count": 5, "id": "2af844557659d096", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 重复值检查 ===\n", "重复行数量: 0\n", "重复行比例: 0.00%\n"]}], "source": ["# 检查重复值\n", "print(\"\\n=== 重复值检查 ===\")\n", "duplicates = df.duplicated().sum()\n", "print(f\"重复行数量: {duplicates}\")\n", "print(f\"重复行比例: {(duplicates / len(df)) * 100:.2f}%\")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "b10dfb4b788c459", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 数据预处理 ===\n", "新特征创建完成\n", "处理后数据形状: (21613, 16)\n"]}], "source": ["# ===== 3. 数据预处理和特征工程 =====\n", "print(\"\\n=== 数据预处理 ===\")\n", "\n", "# 处理日期列\n", "df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')\n", "df['year'] = df['date'].dt.year\n", "df['month'] = df['date'].dt.month\n", "df['day_of_year'] = df['date'].dt.dayofyear\n", "\n", "# 处理yr_renovated列（0表示未翻新）\n", "df['is_renovated'] = (df['yr_renovated'] > 0).astype(int)\n", "df['years_since_renovation'] = np.where(df['yr_renovated'] > 0, \n", "                                       df['year'] - df['yr_renovated'], \n", "                                       df['year'] - df['yr_built'])\n", "\n", "# 房屋年龄\n", "df['house_age'] = df['year'] - df['yr_built']\n", "\n", "# 价格每平方英尺\n", "df['price_per_sqft'] = df['price'] / df['sqft_living']\n", "\n", "print(\"新特征创建完成\")\n", "print(f\"处理后数据形状: {df.shape}\")\n"]}, {"cell_type": "code", "execution_count": 7, "id": "f8fbb3e4ab00483a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 异常值检测 ===\n", "价格异常值数量: 1146 (5.30%)\n", "价格正常范围: $0 - $1,129,575\n", "居住面积异常值数量: 572 (2.65%)\n", "居住面积正常范围: -258 - 4,234 sqft\n", "\n", "价格统计信息:\n", "最小值: $75,000\n", "最大值: $7,700,000\n", "中位数: $450,000\n", "平均值: $540,088\n", "标准差: $367,127\n", "\n", "极端异常值 (0.1%和99.9%分位数外): 41 (0.19%)\n", "极端异常值范围: < $95,000 或 > $3,476,302\n"]}], "source": ["# ===== 4. 异常值检测和处理 =====\n", "print(\"\\n=== 异常值检测 ===\")\n", "\n", "def detect_outliers_iqr(data, column):\n", "    \"\"\"使用改进的IQR方法检测异常值\"\"\"\n", "    Q1 = data[column].quantile(0.25)\n", "    Q3 = data[column].quantile(0.75)\n", "    IQR = Q3 - Q1\n", "    lower_bound = Q1 - 1.5 * IQR\n", "    upper_bound = Q3 + 1.5 * IQR\n", "\n", "    # 对于价格数据，下界不能为负值\n", "    if column == 'price':\n", "        lower_bound = max(0, lower_bound)\n", "\n", "    outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]\n", "    return outliers, lower_bound, upper_bound\n", "\n", "# 检测价格异常值\n", "price_outliers, price_lower, price_upper = detect_outliers_iqr(df, 'price')\n", "print(f\"价格异常值数量: {len(price_outliers)} ({len(price_outliers)/len(df)*100:.2f}%)\")\n", "print(f\"价格正常范围: ${price_lower:,.0f} - ${price_upper:,.0f}\")\n", "\n", "# 检测面积异常值\n", "living_outliers, living_lower, living_upper = detect_outliers_iqr(df, 'sqft_living')\n", "print(f\"居住面积异常值数量: {len(living_outliers)} ({len(living_outliers)/len(df)*100:.2f}%)\")\n", "print(f\"居住面积正常范围: {living_lower:,.0f} - {living_upper:,.0f} sqft\")\n", "\n", "# 额外的价格统计信息\n", "print(f\"\\n价格统计信息:\")\n", "print(f\"最小值: ${df['price'].min():,.0f}\")\n", "print(f\"最大值: ${df['price'].max():,.0f}\")\n", "print(f\"中位数: ${df['price'].median():,.0f}\")\n", "print(f\"平均值: ${df['price'].mean():,.0f}\")\n", "print(f\"标准差: ${df['price'].std():,.0f}\")\n", "\n", "# 使用百分位数方法检测极端异常值\n", "price_99_9 = df['price'].quantile(0.999)\n", "price_0_1 = df['price'].quantile(0.001)\n", "extreme_outliers = df[(df['price'] > price_99_9) | (df['price'] < price_0_1)]\n", "print(f\"\\n极端异常值 (0.1%和99.9%分位数外): {len(extreme_outliers)} ({len(extreme_outliers)/len(df)*100:.2f}%)\")\n", "print(f\"极端异常值范围: < ${price_0_1:,.0f} 或 > ${price_99_9:,.0f}\")\n"]}, {"cell_type": "code", "execution_count": 8, "id": "1791126f6b5ea1f3", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 可视化异常值\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# 价格分布\n", "axes[0,0].hist(df['price'], bins=50, alpha=0.7)\n", "axes[0,0].set_title('房价分布')\n", "axes[0,0].set_xlabel('价格 ($)')\n", "axes[0,0].set_ylabel('频次')\n", "\n", "# 价格箱线图\n", "axes[0,1].boxplot([df['price']])\n", "axes[0,1].set_title('房价箱线图')\n", "axes[0,1].set_ylabel('价格 ($)')\n", "axes[0,1].set_xticklabels(['价格'])\n", "\n", "# 居住面积分布\n", "axes[1,0].hist(df['sqft_living'], bins=50, alpha=0.7)\n", "axes[1,0].set_title('居住面积分布')\n", "axes[1,0].set_xlabel('面积 (sqft)')\n", "axes[1,0].set_ylabel('频次')\n", "\n", "# 居住面积箱线图\n", "axes[1,1].boxplot([df['sqft_living']])\n", "axes[1,1].set_title('居住面积箱线图')\n", "axes[1,1].set_ylabel('面积 (sqft)')\n", "axes[1,1].set_xticklabels(['面积'])\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "51335fa4f7480994", "metadata": {}, "outputs": [], "source": ["# 处理极端异常值（保留合理范围内的数据）\n", "print(\"\\n=== 异常值处理 ===\")\n", "original_size = len(df)\n", "\n", "# 使用更保守的方法移除极端异常值（保留99.5%的数据）\n", "price_99_5th = df['price'].quantile(0.995)\n", "price_0_5th = df['price'].quantile(0.005)\n", "df_clean = df[(df['price'] >= price_0_5th) & (df['price'] <= price_99_5th)]\n", "\n", "# 移除极端面积异常值\n", "living_99_5th = df['sqft_living'].quantile(0.995)\n", "living_0_5th = df['sqft_living'].quantile(0.005)\n", "df_clean = df_clean[(df_clean['sqft_living'] >= living_0_5th) & (df_clean['sqft_living'] <= living_99_5th)]\n", "\n", "# 移除明显不合理的数据\n", "# 价格必须大于0\n", "df_clean = df_clean[df_clean['price'] > 0]\n", "# 居住面积必须大于0\n", "df_clean = df_clean[df_clean['sqft_living'] > 0]\n", "# 土地面积必须大于0\n", "df_clean = df_clean[df_clean['sqft_lot'] > 0]\n", "\n", "print(f\"原始数据量: {original_size}\")\n", "print(f\"清理后数据量: {len(df_clean)}\")\n", "print(f\"移除数据量: {original_size - len(df_clean)} ({(original_size - len(df_clean))/original_size*100:.2f}%)\")\n", "print(f\"清理后价格范围: ${df_clean['price'].min():,.0f} - ${df_clean['price'].max():,.0f}\")\n", "print(f\"清理后居住面积范围: {df_clean['sqft_living'].min():,.0f} - {df_clean['sqft_living'].max():,.0f} sqft\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "c698571558aaeea3", "metadata": {}, "outputs": [], "source": ["# ===== 5. 探索性数据分析 (EDA) =====\n", "print(\"\\n=== 探索性数据分析 ===\")\n", "\n", "# 相关性分析\n", "numeric_columns = ['price', 'sqft_lot', 'sqft_living', 'grade', 'lat', 'long', \n", "                  'yr_built', 'house_age', 'price_per_sqft', 'years_since_renovation']\n", "\n", "correlation_matrix = df_clean[numeric_columns].corr()\n", "\n", "plt.figure(figsize=(12, 10))\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, \n", "            square=True, linewidths=0.5)\n", "plt.title('特征相关性热力图')\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "ad2dac20ef70bccc", "metadata": {}, "outputs": [], "source": ["# 价格与主要特征的关系\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "\n", "# 价格 vs 居住面积\n", "axes[0,0].scatter(df_clean['sqft_living'], df_clean['price'], alpha=0.5)\n", "axes[0,0].set_xlabel('居住面积 (sqft)')\n", "axes[0,0].set_ylabel('价格 ($)')\n", "axes[0,0].set_title('价格 vs 居住面积')\n", "\n", "# 价格 vs 等级\n", "df_clean.boxplot(column='price', by='grade', ax=axes[0,1])\n", "axes[0,1].set_xlabel('建筑等级')\n", "axes[0,1].set_ylabel('价格 ($)')\n", "axes[0,1].set_title('价格 vs 建筑等级')\n", "\n", "# 价格 vs 房屋年龄\n", "axes[0,2].scatter(df_clean['house_age'], df_clean['price'], alpha=0.5)\n", "axes[0,2].set_xlabel('房屋年龄')\n", "axes[0,2].set_ylabel('价格 ($)')\n", "axes[0,2].set_title('价格 vs 房屋年龄')\n", "\n", "# 价格 vs 土地面积\n", "axes[1,0].scatter(df_clean['sqft_lot'], df_clean['price'], alpha=0.5)\n", "axes[1,0].set_xlabel('土地面积 (sqft)')\n", "axes[1,0].set_ylabel('价格 ($)')\n", "axes[1,0].set_title('价格 vs 土地面积')\n", "\n", "# 价格 vs 纬度\n", "axes[1,1].scatter(df_clean['lat'], df_clean['price'], alpha=0.5)\n", "axes[1,1].set_xlabel('纬度')\n", "axes[1,1].set_ylabel('价格 ($)')\n", "axes[1,1].set_title('价格 vs 纬度')\n", "\n", "# 价格 vs 经度\n", "axes[1,2].scatter(df_clean['long'], df_clean['price'], alpha=0.5)\n", "axes[1,2].set_xlabel('经度')\n", "axes[1,2].set_ylabel('价格 ($)')\n", "axes[1,2].set_title('价格 vs 经度')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "fd29ad5804a7ae40", "metadata": {}, "outputs": [], "source": ["# ===== 6. 特征选择和数据准备 =====\n", "print(\"\\n=== 特征选择和数据准备 ===\")\n", "\n", "# 选择用于建模的特征\n", "feature_columns = ['sqft_lot', 'sqft_living', 'grade', 'lat', 'long', \n", "                  'yr_built', 'house_age', 'is_renovated', 'years_since_renovation',\n", "                  'year', 'month']\n", "\n", "X = df_clean[feature_columns].copy()\n", "y = df_clean['price'].copy()\n", "\n", "print(f\"特征数量: {X.shape[1]}\")\n", "print(f\"样本数量: {X.shape[0]}\")\n", "print(\"选择的特征:\", feature_columns)\n"]}, {"cell_type": "code", "execution_count": null, "id": "71294f4a6be4acc", "metadata": {}, "outputs": [], "source": ["# 数据标准化\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "\n", "# 划分训练集和测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "print(f\"训练集大小: {X_train.shape}\")\n", "print(f\"测试集大小: {X_test.shape}\")\n", "\n", "# 标准化特征\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# MinMax标准化（用于某些算法）\n", "minmax_scaler = MinMaxScaler()\n", "X_train_minmax = minmax_scaler.fit_transform(X_train)\n", "X_test_minmax = minmax_scaler.transform(X_test)\n", "\n", "# 重要：对目标变量（价格）也进行标准化\n", "y_scaler = StandardScaler()\n", "y_train_scaled = y_scaler.fit_transform(y_train.values.reshape(-1, 1)).ravel()\n", "y_test_scaled = y_scaler.transform(y_test.values.reshape(-1, 1)).ravel()\n", "\n", "print(\"特征和目标变量标准化完成\")\n", "print(f\"原始价格范围: ${y_train.min():,.0f} - ${y_train.max():,.0f}\")\n", "print(f\"标准化后价格范围: {y_train_scaled.min():.3f} - {y_train_scaled.max():.3f}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "5c2b6f2fe4290b7", "metadata": {}, "outputs": [], "source": ["# ===== 7. 机器学习模型训练和评估 =====\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "from sklearn.linear_model import LinearRegression, Ridge, Lasso\n", "from sklearn.svm import SVR\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "import joblib\n", "\n", "print(\"\\n=== 机器学习模型训练 ===\")\n", "\n", "# 定义评估函数\n", "def evaluate_model(y_true, y_pred, model_name, use_scaled=False):\n", "    \"\"\"评估模型性能\"\"\"\n", "    if use_scaled:\n", "        # 如果使用标准化数据，需要转换回原始尺度\n", "        y_true_orig = y_scaler.inverse_transform(y_true.reshape(-1, 1)).ravel()\n", "        y_pred_orig = y_scaler.inverse_transform(y_pred.reshape(-1, 1)).ravel()\n", "    else:\n", "        y_true_orig = y_true\n", "        y_pred_orig = y_pred\n", "\n", "    mse = mean_squared_error(y_true_orig, y_pred_orig)\n", "    rmse = np.sqrt(mse)\n", "    mae = mean_absolute_error(y_true_orig, y_pred_orig)\n", "    r2 = r2_score(y_true_orig, y_pred_orig)\n", "\n", "    print(f\"\\n{model_name} 模型评估:\")\n", "    print(f\"均方误差 (MSE): ${mse:,.0f}\")\n", "    print(f\"均方根误差 (RMSE): ${rmse:,.0f}\")\n", "    print(f\"平均绝对误差 (MAE): ${mae:,.0f}\")\n", "    print(f\"决定系数 (R²): {r2:.4f}\")\n", "\n", "    return {'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'R2': r2}\n", "\n", "# 存储模型结果\n", "model_results = {}\n"]}, {"cell_type": "code", "execution_count": null, "id": "a760cb89d65a990d", "metadata": {}, "outputs": [], "source": ["# 1. 线性回归（使用标准化数据）\n", "print(\"训练线性回归模型...\")\n", "lr_model = LinearRegression()\n", "lr_model.fit(X_train_scaled, y_train_scaled)\n", "lr_pred_scaled = lr_model.predict(X_test_scaled)\n", "model_results['Linear Regression'] = evaluate_model(y_test_scaled, lr_pred_scaled, \"线性回归\", use_scaled=True)\n"]}, {"cell_type": "code", "execution_count": null, "id": "9f7f1e38d053075f", "metadata": {}, "outputs": [], "source": ["# 2. Ridge回归（使用标准化数据）\n", "print(\"训练Ridge回归模型...\")\n", "ridge_model = Ridge(alpha=1.0, random_state=42)\n", "ridge_model.fit(X_train_scaled, y_train_scaled)\n", "ridge_pred_scaled = ridge_model.predict(X_test_scaled)\n", "model_results['Ridge'] = evaluate_model(y_test_scaled, ridge_pred_scaled, \"Ridge回归\", use_scaled=True)\n"]}, {"cell_type": "code", "execution_count": null, "id": "df4de73230d6cd61", "metadata": {}, "outputs": [], "source": ["# 3. <PERSON><PERSON>回归（使用标准化数据）\n", "print(\"训练Lasso回归模型...\")\n", "lasso_model = Lasso(alpha=0.1, random_state=42)  # 降低alpha值\n", "lasso_model.fit(X_train_scaled, y_train_scaled)\n", "lasso_pred_scaled = lasso_model.predict(X_test_scaled)\n", "model_results['Lasso'] = evaluate_model(y_test_scaled, lasso_pred_scaled, \"Lasso回归\", use_scaled=True)\n"]}, {"cell_type": "code", "execution_count": null, "id": "c025b3b404799d6d", "metadata": {}, "outputs": [], "source": ["# 4. 随机森林（使用原始数据，因为树模型对尺度不敏感）\n", "print(\"训练随机森林模型...\")\n", "rf_model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)\n", "rf_model.fit(X_train, y_train)\n", "rf_pred = rf_model.predict(X_test)\n", "model_results['Random Forest'] = evaluate_model(y_test, rf_pred, \"随机森林\", use_scaled=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "61fd4af8f33210da", "metadata": {}, "outputs": [], "source": ["# 5. 梯度提升（使用原始数据）\n", "print(\"训练梯度提升模型...\")\n", "gb_model = GradientBoostingRegressor(n_estimators=100, random_state=42)\n", "gb_model.fit(X_train, y_train)\n", "gb_pred = gb_model.predict(X_test)\n", "model_results['Gradient Boosting'] = evaluate_model(y_test, gb_pred, \"梯度提升\", use_scaled=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "d23b5d74805906bc", "metadata": {}, "outputs": [], "source": ["# 6. 支持向量回归（使用标准化数据）\n", "print(\"训练支持向量回归模型...\")\n", "svr_model = SVR(kernel='rbf', C=100, gamma='scale')  # 降低C值\n", "svr_model.fit(X_train_scaled, y_train_scaled)\n", "svr_pred_scaled = svr_model.predict(X_test_scaled)\n", "model_results['SVR'] = evaluate_model(y_test_scaled, svr_pred_scaled, \"支持向量回归\", use_scaled=True)\n"]}, {"cell_type": "code", "execution_count": null, "id": "6280afc9c0f98e0", "metadata": {}, "outputs": [], "source": ["# ===== 8. 模型比较和可视化 =====\n", "print(\"\\n=== 模型性能比较 ===\")\n", "\n", "# 创建比较表格\n", "comparison_df = pd.DataFrame(model_results).T\n", "comparison_df = comparison_df.round(2)\n", "print(comparison_df)\n", "\n", "# 找出最佳模型\n", "best_model_name = comparison_df['R2'].idxmax()\n", "print(f\"\\n最佳模型: {best_model_name} (R² = {comparison_df.loc[best_model_name, 'R2']:.4f})\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "22bc28a2aeed2aa4", "metadata": {}, "outputs": [], "source": ["# 可视化模型比较\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# R²比较\n", "axes[0,0].bar(comparison_df.index, comparison_df['R2'])\n", "axes[0,0].set_title('模型R²比较')\n", "axes[0,0].set_ylabel('R²')\n", "axes[0,0].tick_params(axis='x', rotation=45)\n", "\n", "# RMSE比较\n", "axes[0,1].bar(comparison_df.index, comparison_df['RMSE'])\n", "axes[0,1].set_title('模型RMSE比较')\n", "axes[0,1].set_ylabel('RMSE')\n", "axes[0,1].tick_params(axis='x', rotation=45)\n", "\n", "# MAE比较\n", "axes[1,0].bar(comparison_df.index, comparison_df['MAE'])\n", "axes[1,0].set_title('模型MAE比较')\n", "axes[1,0].set_ylabel('MAE')\n", "axes[1,0].tick_params(axis='x', rotation=45)\n", "\n", "# MSE比较\n", "axes[1,1].bar(comparison_df.index, comparison_df['MSE'])\n", "axes[1,1].set_title('模型MSE比较')\n", "axes[1,1].set_ylabel('MSE')\n", "axes[1,1].tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "22e462c189a28891", "metadata": {}, "outputs": [], "source": ["# 预测值 vs 实际值可视化\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "\n", "# 转换标准化的预测值回原始尺度\n", "lr_pred_orig = y_scaler.inverse_transform(lr_pred_scaled.reshape(-1, 1)).ravel()\n", "ridge_pred_orig = y_scaler.inverse_transform(ridge_pred_scaled.reshape(-1, 1)).ravel()\n", "lasso_pred_orig = y_scaler.inverse_transform(lasso_pred_scaled.reshape(-1, 1)).ravel()\n", "svr_pred_orig = y_scaler.inverse_transform(svr_pred_scaled.reshape(-1, 1)).ravel()\n", "\n", "predictions = [lr_pred_orig, ridge_pred_orig, lasso_pred_orig, rf_pred, gb_pred, svr_pred_orig]\n", "model_names = ['Linear Regression', '<PERSON>', '<PERSON><PERSON>', 'Random Forest', 'Gradient Boosting', 'SVR']\n", "\n", "for i, (pred, name) in enumerate(zip(predictions, model_names)):\n", "    row = i // 3\n", "    col = i % 3\n", "\n", "    axes[row, col].scatter(y_test, pred, alpha=0.5)\n", "    axes[row, col].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "    axes[row, col].set_xlabel('实际价格 ($)')\n", "    axes[row, col].set_ylabel('预测价格 ($)')\n", "    axes[row, col].set_title(f'{name}')\n", "\n", "    # 添加R²值\n", "    r2 = model_results[name]['R2']\n", "    axes[row, col].text(0.05, 0.95, f'R² = {r2:.3f}', transform=axes[row, col].transAxes,\n", "                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "ac211906423564c9", "metadata": {}, "outputs": [], "source": ["# ===== 9. 特征重要性分析 =====\n", "print(\"\\n=== 特征重要性分析 ===\")\n", "\n", "# 随机森林特征重要性\n", "rf_importance = pd.DataFrame({\n", "    '特征': feature_columns,\n", "    '重要性': rf_model.feature_importances_\n", "}).sort_values('重要性', ascending=False)\n", "\n", "print(\"随机森林特征重要性:\")\n", "print(rf_importance)\n", "\n", "# 梯度提升特征重要性\n", "gb_importance = pd.DataFrame({\n", "    '特征': feature_columns,\n", "    '重要性': gb_model.feature_importances_\n", "}).sort_values('重要性', ascending=False)\n", "\n", "print(\"\\n梯度提升特征重要性:\")\n", "print(gb_importance)\n"]}, {"cell_type": "code", "execution_count": null, "id": "c91ea56969fc6fd6", "metadata": {}, "outputs": [], "source": ["# 可视化特征重要性\n", "fig, axes = plt.subplots(1, 2, figsize=(16, 6))\n", "\n", "# 随机森林特征重要性\n", "axes[0].barh(rf_importance['特征'], rf_importance['重要性'])\n", "axes[0].set_title('随机森林 - 特征重要性')\n", "axes[0].set_xlabel('重要性')\n", "\n", "# 梯度提升特征重要性\n", "axes[1].barh(gb_importance['特征'], gb_importance['重要性'])\n", "axes[1].set_title('梯度提升 - 特征重要性')\n", "axes[1].set_xlabel('重要性')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "b041a13a5269ca71", "metadata": {}, "outputs": [], "source": ["# ===== 10. 残差分析 =====\n", "print(\"\\n=== 残差分析 ===\")\n", "\n", "# 选择最佳模型进行残差分析\n", "if best_model_name == 'Random Forest':\n", "    best_pred = rf_pred\n", "    best_model = rf_model\n", "elif best_model_name == 'Gradient Boosting':\n", "    best_pred = gb_pred\n", "    best_model = gb_model\n", "elif best_model_name == 'Linear Regression':\n", "    best_pred = lr_pred_orig\n", "    best_model = lr_model\n", "elif best_model_name == 'Ridge':\n", "    best_pred = ridge_pred_orig\n", "    best_model = ridge_model\n", "elif best_model_name == '<PERSON><PERSON>':\n", "    best_pred = lasso_pred_orig\n", "    best_model = lasso_model\n", "else:\n", "    best_pred = svr_pred_orig\n", "    best_model = svr_model\n", "\n", "residuals = y_test - best_pred\n", "\n", "# 残差分析图\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# 残差 vs 预测值\n", "axes[0,0].scatter(best_pred, residuals, alpha=0.5)\n", "axes[0,0].axhline(y=0, color='r', linestyle='--')\n", "axes[0,0].set_xlabel('预测值')\n", "axes[0,0].set_ylabel('残差')\n", "axes[0,0].set_title('残差 vs 预测值')\n", "\n", "# 残差直方图\n", "axes[0,1].hist(residuals, bins=50, alpha=0.7)\n", "axes[0,1].set_xlabel('残差')\n", "axes[0,1].set_ylabel('频次')\n", "axes[0,1].set_title('残差分布')\n", "\n", "# Q-Q图\n", "from scipy import stats\n", "stats.probplot(residuals, dist=\"norm\", plot=axes[1,0])\n", "axes[1,0].set_title('残差Q-Q图')\n", "\n", "# 残差 vs 实际值\n", "axes[1,1].scatter(y_test, residuals, alpha=0.5)\n", "axes[1,1].axhline(y=0, color='r', linestyle='--')\n", "axes[1,1].set_xlabel('实际值')\n", "axes[1,1].set_ylabel('残差')\n", "axes[1,1].set_title('残差 vs 实际值')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "57d49c8a68587ca7", "metadata": {}, "outputs": [], "source": ["# ===== 11. 模型保存 =====\n", "print(\"\\n=== 模型保存 ===\")\n", "\n", "# 创建模型目录\n", "model_dir = 'model'\n", "if not os.path.exists(model_dir):\n", "    os.makedirs(model_dir)\n", "\n", "# 保存最佳模型\n", "best_model_path = os.path.join(model_dir, f'best_model_{best_model_name.lower().replace(\" \", \"_\")}.pkl')\n", "joblib.dump(best_model, best_model_path)\n", "print(f\"最佳模型已保存到: {best_model_path}\")\n", "\n", "# 保存特征标准化器\n", "scaler_path = os.path.join(model_dir, 'feature_scaler.pkl')\n", "joblib.dump(scaler, scaler_path)\n", "print(f\"特征标准化器已保存到: {scaler_path}\")\n", "\n", "# 保存目标变量标准化器\n", "y_scaler_path = os.path.join(model_dir, 'target_scaler.pkl')\n", "joblib.dump(y_scaler, y_scaler_path)\n", "print(f\"目标变量标准化器已保存到: {y_scaler_path}\")\n", "\n", "# 保存特征列名\n", "feature_path = os.path.join(model_dir, 'feature_columns.pkl')\n", "joblib.dump(feature_columns, feature_path)\n", "print(f\"特征列名已保存到: {feature_path}\")\n", "\n", "# 保存模型比较结果\n", "results_path = os.path.join(model_dir, 'model_comparison.csv')\n", "comparison_df.to_csv(results_path)\n", "print(f\"模型比较结果已保存到: {results_path}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "d96c5c6a22435d56", "metadata": {}, "outputs": [], "source": ["# ===== 12. 业务洞察和总结 =====\n", "print(\"\\n=== 业务洞察和总结 ===\")\n", "\n", "print(\"1. 数据质量:\")\n", "print(f\"   - 原始数据: {original_size:,} 条记录\")\n", "print(f\"   - 清理后数据: {len(df_clean):,} 条记录\")\n", "print(f\"   - 数据清理率: {(original_size - len(df_clean))/original_size*100:.1f}%\")\n", "\n", "print(\"\\n2. 模型性能:\")\n", "print(f\"   - 最佳模型: {best_model_name}\")\n", "print(f\"   - R²得分: {comparison_df.loc[best_model_name, 'R2']:.4f}\")\n", "print(f\"   - RMSE: ${comparison_df.loc[best_model_name, 'RMSE']:,.0f}\")\n", "print(f\"   - MAE: ${comparison_df.loc[best_model_name, 'MAE']:,.0f}\")\n", "\n", "print(\"\\n3. 关键特征 (基于随机森林):\")\n", "for i, row in rf_importance.head(5).iterrows():\n", "    print(f\"   - {row['特征']}: {row['重要性']:.3f}\")\n", "\n", "print(\"\\n4. 业务建议:\")\n", "print(\"   - 居住面积是影响房价的最重要因素\")\n", "print(\"   - 建筑等级和地理位置也显著影响房价\")\n", "print(\"   - 模型可用于房价估算，但需要考虑市场变化\")\n", "print(\"   - 建议定期更新模型以适应市场变化\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "1938e82164d54e4c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 5}