{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2025-08-02T06:08:18.700341Z", "start_time": "2025-08-02T06:08:18.686321Z"}}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import os\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置支持中文的字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体\n", "plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示为方块的问题\n"]}, {"cell_type": "code", "execution_count": 2, "id": "1aff6b030391d644", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 数据加载 ===\n", "数据集形状: (21613, 9)\n", "数据集列名: ['date', 'price', 'sqft_lot', 'sqft_living', 'grade', 'lat', 'long', 'yr_built', 'yr_renovated']\n", "\n", "数据集前5行:\n", "       date   price  sqft_lot  sqft_living  grade      lat     long  yr_built  \\\n", "0  20141013  221900      5650         1180      7  47.5112 -122.257      1955   \n", "1  20141209  538000      7242         2570      7  47.7210 -122.319      1951   \n", "2  20150225  180000     10000          770      6  47.7379 -122.233      1933   \n", "3  20141209  604000      5000         1960      7  47.5208 -122.393      1965   \n", "4  20150218  510000      8080         1680      8  47.6168 -122.045      1987   \n", "\n", "   yr_renovated  \n", "0             0  \n", "1          1991  \n", "2             0  \n", "3             0  \n", "4             0  \n"]}], "source": ["# ===== 1. 数据加载和初步探索 =====\n", "print(\"=== 数据加载 ===\")\n", "df = pd.read_csv('data.csv')\n", "print(f\"数据集形状: {df.shape}\")\n", "print(f\"数据集列名: {df.columns.tolist()}\")\n", "print(\"\\n数据集前5行:\")\n", "print(df.head())\n"]}, {"cell_type": "code", "execution_count": 3, "id": "2ba32048eb9e8a0d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 数据基本信息 ===\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 21613 entries, 0 to 21612\n", "Data columns (total 9 columns):\n", " #   Column        Non-Null Count  Dtype  \n", "---  ------        --------------  -----  \n", " 0   date          21613 non-null  int64  \n", " 1   price         21613 non-null  int64  \n", " 2   sqft_lot      21613 non-null  int64  \n", " 3   sqft_living   21613 non-null  int64  \n", " 4   grade         21613 non-null  int64  \n", " 5   lat           21613 non-null  float64\n", " 6   long          21613 non-null  float64\n", " 7   yr_built      21613 non-null  int64  \n", " 8   yr_renovated  21613 non-null  int64  \n", "dtypes: float64(2), int64(7)\n", "memory usage: 1.5 MB\n", "None\n", "\n", "数据统计描述:\n", "               date         price      sqft_lot   sqft_living         grade  \\\n", "count  2.161300e+04  2.161300e+04  2.161300e+04  21613.000000  21613.000000   \n", "mean   2.014390e+07  5.400881e+05  1.510697e+04   2079.899736      7.656873   \n", "std    4.436582e+03  3.671272e+05  4.142051e+04    918.440897      1.175459   \n", "min    2.014050e+07  7.500000e+04  5.200000e+02    290.000000      1.000000   \n", "25%    2.014072e+07  3.219500e+05  5.040000e+03   1427.000000      7.000000   \n", "50%    2.014102e+07  4.500000e+05  7.618000e+03   1910.000000      7.000000   \n", "75%    2.015022e+07  6.450000e+05  1.068800e+04   2550.000000      8.000000   \n", "max    2.015053e+07  7.700000e+06  1.651359e+06  13540.000000     13.000000   \n", "\n", "                lat          long      yr_built  yr_renovated  \n", "count  21613.000000  21613.000000  21613.000000  21613.000000  \n", "mean      47.560053   -122.213896   1971.005136     84.402258  \n", "std        0.138564      0.140828     29.373411    401.679240  \n", "min       47.155900   -122.519000   1900.000000      0.000000  \n", "25%       47.471000   -122.328000   1951.000000      0.000000  \n", "50%       47.571800   -122.230000   1975.000000      0.000000  \n", "75%       47.678000   -122.125000   1997.000000      0.000000  \n", "max       47.777600   -121.315000   2015.000000   2015.000000  \n"]}], "source": ["# 数据基本信息\n", "print(\"\\n=== 数据基本信息 ===\")\n", "print(df.info())\n", "print(\"\\n数据统计描述:\")\n", "print(df.describe())\n"]}, {"cell_type": "code", "execution_count": 4, "id": "57fa28f77838dbdb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 缺失值检查 ===\n", "各列缺失值数量:\n", "date            0\n", "price           0\n", "sqft_lot        0\n", "sqft_living     0\n", "grade           0\n", "lat             0\n", "long            0\n", "yr_built        0\n", "yr_renovated    0\n", "dtype: int64\n", "\n", "总缺失值数量: 0\n", "缺失值比例: 0.00%\n"]}], "source": ["# ===== 2. 数据质量检查 =====\n", "print(\"\\n=== 缺失值检查 ===\")\n", "missing_values = df.isnull().sum()\n", "print(\"各列缺失值数量:\")\n", "print(missing_values)\n", "print(f\"\\n总缺失值数量: {missing_values.sum()}\")\n", "print(f\"缺失值比例: {(missing_values.sum() / (df.shape[0] * df.shape[1])) * 100:.2f}%\")\n"]}, {"cell_type": "code", "execution_count": 5, "id": "4109420a56d3db14", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 重复值检查 ===\n", "重复行数量: 0\n", "重复行比例: 0.00%\n"]}], "source": ["# 检查重复值\n", "print(\"\\n=== 重复值检查 ===\")\n", "duplicates = df.duplicated().sum()\n", "print(f\"重复行数量: {duplicates}\")\n", "print(f\"重复行比例: {(duplicates / len(df)) * 100:.2f}%\")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "7d318283f230b069", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 数据预处理 ===\n", "新特征创建完成\n", "处理后数据形状: (21613, 16)\n"]}], "source": ["# ===== 3. 数据预处理和特征工程 =====\n", "print(\"\\n=== 数据预处理 ===\")\n", "\n", "# 处理日期列\n", "df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')\n", "df['year'] = df['date'].dt.year\n", "df['month'] = df['date'].dt.month\n", "df['day_of_year'] = df['date'].dt.dayofyear\n", "\n", "# 处理yr_renovated列（0表示未翻新）\n", "df['is_renovated'] = (df['yr_renovated'] > 0).astype(int)\n", "df['years_since_renovation'] = np.where(df['yr_renovated'] > 0, \n", "                                       df['year'] - df['yr_renovated'], \n", "                                       df['year'] - df['yr_built'])\n", "\n", "# 房屋年龄\n", "df['house_age'] = df['year'] - df['yr_built']\n", "\n", "# 价格每平方英尺\n", "df['price_per_sqft'] = df['price'] / df['sqft_living']\n", "\n", "print(\"新特征创建完成\")\n", "print(f\"处理后数据形状: {df.shape}\")\n"]}, {"cell_type": "code", "execution_count": 7, "id": "e14a649325d6c8cf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 异常值检测 ===\n", "价格异常值数量: 1146 (5.30%)\n", "价格正常范围: $-162,625 - $1,129,575\n", "居住面积异常值数量: 572 (2.65%)\n"]}], "source": ["# ===== 4. 异常值检测和处理 =====\n", "print(\"\\n=== 异常值检测 ===\")\n", "\n", "def detect_outliers_iqr(data, column):\n", "    \"\"\"使用IQR方法检测异常值\"\"\"\n", "    Q1 = data[column].quantile(0.25)\n", "    Q3 = data[column].quantile(0.75)\n", "    IQR = Q3 - Q1\n", "    lower_bound = Q1 - 1.5 * IQR\n", "    upper_bound = Q3 + 1.5 * IQR\n", "    outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]\n", "    return outliers, lower_bound, upper_bound\n", "\n", "# 检测价格异常值\n", "price_outliers, price_lower, price_upper = detect_outliers_iqr(df, 'price')\n", "print(f\"价格异常值数量: {len(price_outliers)} ({len(price_outliers)/len(df)*100:.2f}%)\")\n", "print(f\"价格正常范围: ${price_lower:,.0f} - ${price_upper:,.0f}\")\n", "\n", "# 检测面积异常值\n", "living_outliers, living_lower, living_upper = detect_outliers_iqr(df, 'sqft_living')\n", "print(f\"居住面积异常值数量: {len(living_outliers)} ({len(living_outliers)/len(df)*100:.2f}%)\")\n"]}, {"cell_type": "code", "execution_count": 8, "id": "47f87eb472528a5c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 可视化异常值\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# 价格分布\n", "axes[0,0].hist(df['price'], bins=50, alpha=0.7)\n", "axes[0,0].set_title('房价分布')\n", "axes[0,0].set_xlabel('价格 ($)')\n", "axes[0,0].set_ylabel('频次')\n", "\n", "# 价格箱线图\n", "axes[0,1].boxplot(df['price'])\n", "axes[0,1].set_title('房价箱线图')\n", "axes[0,1].set_ylabel('价格 ($)')\n", "\n", "# 居住面积分布\n", "axes[1,0].hist(df['sqft_living'], bins=50, alpha=0.7)\n", "axes[1,0].set_title('居住面积分布')\n", "axes[1,0].set_xlabel('面积 (sqft)')\n", "axes[1,0].set_ylabel('频次')\n", "\n", "# 居住面积箱线图\n", "axes[1,1].boxplot(df['sqft_living'])\n", "axes[1,1].set_title('居住面积箱线图')\n", "axes[1,1].set_ylabel('面积 (sqft)')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "f89fee877acc99c6", "metadata": {}, "outputs": [], "source": ["# 处理极端异常值（保留合理范围内的数据）\n", "print(\"\\n=== 异常值处理 ===\")\n", "original_size = len(df)\n", "\n", "# 移除极端价格异常值（保留99%的数据）\n", "price_99th = df['price'].quantile(0.99)\n", "price_1st = df['price'].quantile(0.01)\n", "df_clean = df[(df['price'] >= price_1st) & (df['price'] <= price_99th)]\n", "\n", "# 移除极端面积异常值\n", "living_99th = df['sqft_living'].quantile(0.99)\n", "living_1st = df['sqft_living'].quantile(0.01)\n", "df_clean = df_clean[(df_clean['sqft_living'] >= living_1st) & (df_clean['sqft_living'] <= living_99th)]\n", "\n", "print(f\"原始数据量: {original_size}\")\n", "print(f\"清理后数据量: {len(df_clean)}\")\n", "print(f\"移除数据量: {original_size - len(df_clean)} ({(original_size - len(df_clean))/original_size*100:.2f}%)\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "9c0c682ab4e56af9", "metadata": {}, "outputs": [], "source": ["# ===== 5. 探索性数据分析 (EDA) =====\n", "print(\"\\n=== 探索性数据分析 ===\")\n", "\n", "# 相关性分析\n", "numeric_columns = ['price', 'sqft_lot', 'sqft_living', 'grade', 'lat', 'long', \n", "                  'yr_built', 'house_age', 'price_per_sqft', 'years_since_renovation']\n", "\n", "correlation_matrix = df_clean[numeric_columns].corr()\n", "\n", "plt.figure(figsize=(12, 10))\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, \n", "            square=True, linewidths=0.5)\n", "plt.title('特征相关性热力图')\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "22942fac55ff1358", "metadata": {}, "outputs": [], "source": ["# 价格与主要特征的关系\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "\n", "# 价格 vs 居住面积\n", "axes[0,0].scatter(df_clean['sqft_living'], df_clean['price'], alpha=0.5)\n", "axes[0,0].set_xlabel('居住面积 (sqft)')\n", "axes[0,0].set_ylabel('价格 ($)')\n", "axes[0,0].set_title('价格 vs 居住面积')\n", "\n", "# 价格 vs 等级\n", "df_clean.boxplot(column='price', by='grade', ax=axes[0,1])\n", "axes[0,1].set_xlabel('建筑等级')\n", "axes[0,1].set_ylabel('价格 ($)')\n", "axes[0,1].set_title('价格 vs 建筑等级')\n", "\n", "# 价格 vs 房屋年龄\n", "axes[0,2].scatter(df_clean['house_age'], df_clean['price'], alpha=0.5)\n", "axes[0,2].set_xlabel('房屋年龄')\n", "axes[0,2].set_ylabel('价格 ($)')\n", "axes[0,2].set_title('价格 vs 房屋年龄')\n", "\n", "# 价格 vs 土地面积\n", "axes[1,0].scatter(df_clean['sqft_lot'], df_clean['price'], alpha=0.5)\n", "axes[1,0].set_xlabel('土地面积 (sqft)')\n", "axes[1,0].set_ylabel('价格 ($)')\n", "axes[1,0].set_title('价格 vs 土地面积')\n", "\n", "# 价格 vs 纬度\n", "axes[1,1].scatter(df_clean['lat'], df_clean['price'], alpha=0.5)\n", "axes[1,1].set_xlabel('纬度')\n", "axes[1,1].set_ylabel('价格 ($)')\n", "axes[1,1].set_title('价格 vs 纬度')\n", "\n", "# 价格 vs 经度\n", "axes[1,2].scatter(df_clean['long'], df_clean['price'], alpha=0.5)\n", "axes[1,2].set_xlabel('经度')\n", "axes[1,2].set_ylabel('价格 ($)')\n", "axes[1,2].set_title('价格 vs 经度')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "b13b0c1332727b7e", "metadata": {}, "outputs": [], "source": ["# ===== 6. 特征选择和数据准备 =====\n", "print(\"\\n=== 特征选择和数据准备 ===\")\n", "\n", "# 选择用于建模的特征\n", "feature_columns = ['sqft_lot', 'sqft_living', 'grade', 'lat', 'long', \n", "                  'yr_built', 'house_age', 'is_renovated', 'years_since_renovation',\n", "                  'year', 'month']\n", "\n", "X = df_clean[feature_columns].copy()\n", "y = df_clean['price'].copy()\n", "\n", "print(f\"特征数量: {X.shape[1]}\")\n", "print(f\"样本数量: {X.shape[0]}\")\n", "print(\"选择的特征:\", feature_columns)\n"]}, {"cell_type": "code", "execution_count": null, "id": "8ac8087e29e9c309", "metadata": {}, "outputs": [], "source": ["# 数据标准化\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "\n", "# 划分训练集和测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "print(f\"训练集大小: {X_train.shape}\")\n", "print(f\"测试集大小: {X_test.shape}\")\n", "\n", "# 标准化特征\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# MinMax标准化（用于某些算法）\n", "minmax_scaler = MinMaxScaler()\n", "X_train_minmax = minmax_scaler.fit_transform(X_train)\n", "X_test_minmax = minmax_scaler.transform(X_test)\n", "\n", "print(\"数据标准化完成\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "39f3d65004a64997", "metadata": {}, "outputs": [], "source": ["# ===== 7. 机器学习模型训练和评估 =====\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "from sklearn.linear_model import LinearRegression, Ridge, Lasso\n", "from sklearn.svm import SVR\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "import joblib\n", "\n", "print(\"\\n=== 机器学习模型训练 ===\")\n", "\n", "# 定义评估函数\n", "def evaluate_model(y_true, y_pred, model_name):\n", "    \"\"\"评估模型性能\"\"\"\n", "    mse = mean_squared_error(y_true, y_pred)\n", "    rmse = np.sqrt(mse)\n", "    mae = mean_absolute_error(y_true, y_pred)\n", "    r2 = r2_score(y_true, y_pred)\n", "    \n", "    print(f\"\\n{model_name} 模型评估:\")\n", "    print(f\"均方误差 (MSE): {mse:,.2f}\")\n", "    print(f\"均方根误差 (RMSE): {rmse:,.2f}\")\n", "    print(f\"平均绝对误差 (MAE): {mae:,.2f}\")\n", "    print(f\"决定系数 (R²): {r2:.4f}\")\n", "    \n", "    return {'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'R2': r2}\n", "\n", "# 存储模型结果\n", "model_results = {}\n"]}, {"cell_type": "code", "execution_count": null, "id": "20327fcb560a0b67", "metadata": {}, "outputs": [], "source": ["# 1. 线性回归\n", "print(\"训练线性回归模型...\")\n", "lr_model = LinearRegression()\n", "lr_model.fit(X_train_scaled, y_train)\n", "lr_pred = lr_model.predict(X_test_scaled)\n", "model_results['Linear Regression'] = evaluate_model(y_test, lr_pred, \"线性回归\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "47c28315ccf8265f", "metadata": {}, "outputs": [], "source": ["# 2. <PERSON>回归\n", "print(\"训练Ridge回归模型...\")\n", "ridge_model = Ridge(alpha=1.0, random_state=42)\n", "ridge_model.fit(X_train_scaled, y_train)\n", "ridge_pred = ridge_model.predict(X_test_scaled)\n", "model_results['Ridge'] = evaluate_model(y_test, ridge_pred, \"Ridge回归\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "e5cb2f8d8711371c", "metadata": {}, "outputs": [], "source": ["# 3. <PERSON><PERSON>回归\n", "print(\"训练Lasso回归模型...\")\n", "lasso_model = Lasso(alpha=1000.0, random_state=42)\n", "lasso_model.fit(X_train_scaled, y_train)\n", "lasso_pred = lasso_model.predict(X_test_scaled)\n", "model_results['Lasso'] = evaluate_model(y_test, lasso_pred, \"Lasso回归\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "f788ca24bae6862d", "metadata": {}, "outputs": [], "source": ["# 4. 随机森林\n", "print(\"训练随机森林模型...\")\n", "rf_model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)\n", "rf_model.fit(X_train, y_train)\n", "rf_pred = rf_model.predict(X_test)\n", "model_results['Random Forest'] = evaluate_model(y_test, rf_pred, \"随机森林\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "88964deadb2d825f", "metadata": {}, "outputs": [], "source": ["# 5. 梯度提升\n", "print(\"训练梯度提升模型...\")\n", "gb_model = GradientBoostingRegressor(n_estimators=100, random_state=42)\n", "gb_model.fit(X_train, y_train)\n", "gb_pred = gb_model.predict(X_test)\n", "model_results['Gradient Boosting'] = evaluate_model(y_test, gb_pred, \"梯度提升\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "a0f33f614011371f", "metadata": {}, "outputs": [], "source": ["# 6. 支持向量回归\n", "print(\"训练支持向量回归模型...\")\n", "svr_model = SVR(kernel='rbf', C=1000, gamma='scale')\n", "svr_model.fit(X_train_scaled, y_train)\n", "svr_pred = svr_model.predict(X_test_scaled)\n", "model_results['SVR'] = evaluate_model(y_test, svr_pred, \"支持向量回归\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "e953165f2154ced9", "metadata": {}, "outputs": [], "source": ["# ===== 8. 模型比较和可视化 =====\n", "print(\"\\n=== 模型性能比较 ===\")\n", "\n", "# 创建比较表格\n", "comparison_df = pd.DataFrame(model_results).T\n", "comparison_df = comparison_df.round(2)\n", "print(comparison_df)\n", "\n", "# 找出最佳模型\n", "best_model_name = comparison_df['R2'].idxmax()\n", "print(f\"\\n最佳模型: {best_model_name} (R² = {comparison_df.loc[best_model_name, 'R2']:.4f})\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "4873b7bebd26cb91", "metadata": {}, "outputs": [], "source": ["# 可视化模型比较\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# R²比较\n", "axes[0,0].bar(comparison_df.index, comparison_df['R2'])\n", "axes[0,0].set_title('模型R²比较')\n", "axes[0,0].set_ylabel('R²')\n", "axes[0,0].tick_params(axis='x', rotation=45)\n", "\n", "# RMSE比较\n", "axes[0,1].bar(comparison_df.index, comparison_df['RMSE'])\n", "axes[0,1].set_title('模型RMSE比较')\n", "axes[0,1].set_ylabel('RMSE')\n", "axes[0,1].tick_params(axis='x', rotation=45)\n", "\n", "# MAE比较\n", "axes[1,0].bar(comparison_df.index, comparison_df['MAE'])\n", "axes[1,0].set_title('模型MAE比较')\n", "axes[1,0].set_ylabel('MAE')\n", "axes[1,0].tick_params(axis='x', rotation=45)\n", "\n", "# MSE比较\n", "axes[1,1].bar(comparison_df.index, comparison_df['MSE'])\n", "axes[1,1].set_title('模型MSE比较')\n", "axes[1,1].set_ylabel('MSE')\n", "axes[1,1].tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "c28e37088dcc3dd4", "metadata": {}, "outputs": [], "source": ["# 预测值 vs 实际值可视化\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "predictions = [lr_pred, ridge_pred, lasso_pred, rf_pred, gb_pred, svr_pred]\n", "model_names = ['Linear Regression', '<PERSON>', '<PERSON><PERSON>', 'Random Forest', 'Gradient Boosting', 'SVR']\n", "\n", "for i, (pred, name) in enumerate(zip(predictions, model_names)):\n", "    row = i // 3\n", "    col = i % 3\n", "\n", "    axes[row, col].scatter(y_test, pred, alpha=0.5)\n", "    axes[row, col].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "    axes[row, col].set_xlabel('实际价格')\n", "    axes[row, col].set_ylabel('预测价格')\n", "    axes[row, col].set_title(f'{name}')\n", "\n", "    # 添加R²值\n", "    r2 = model_results[name]['R2']\n", "    axes[row, col].text(0.05, 0.95, f'R² = {r2:.3f}', transform=axes[row, col].transAxes,\n", "                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "12ed6ed976a81717", "metadata": {}, "outputs": [], "source": ["# ===== 9. 特征重要性分析 =====\n", "print(\"\\n=== 特征重要性分析 ===\")\n", "\n", "# 随机森林特征重要性\n", "rf_importance = pd.DataFrame({\n", "    '特征': feature_columns,\n", "    '重要性': rf_model.feature_importances_\n", "}).sort_values('重要性', ascending=False)\n", "\n", "print(\"随机森林特征重要性:\")\n", "print(rf_importance)\n", "\n", "# 梯度提升特征重要性\n", "gb_importance = pd.DataFrame({\n", "    '特征': feature_columns,\n", "    '重要性': gb_model.feature_importances_\n", "}).sort_values('重要性', ascending=False)\n", "\n", "print(\"\\n梯度提升特征重要性:\")\n", "print(gb_importance)\n"]}, {"cell_type": "code", "execution_count": null, "id": "a6fb7cf5a5803e0e", "metadata": {}, "outputs": [], "source": ["# 可视化特征重要性\n", "fig, axes = plt.subplots(1, 2, figsize=(16, 6))\n", "\n", "# 随机森林特征重要性\n", "axes[0].barh(rf_importance['特征'], rf_importance['重要性'])\n", "axes[0].set_title('随机森林 - 特征重要性')\n", "axes[0].set_xlabel('重要性')\n", "\n", "# 梯度提升特征重要性\n", "axes[1].barh(gb_importance['特征'], gb_importance['重要性'])\n", "axes[1].set_title('梯度提升 - 特征重要性')\n", "axes[1].set_xlabel('重要性')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "46191aaa3eec8ab4", "metadata": {}, "outputs": [], "source": ["# ===== 10. 残差分析 =====\n", "print(\"\\n=== 残差分析 ===\")\n", "\n", "# 选择最佳模型进行残差分析\n", "if best_model_name == 'Random Forest':\n", "    best_pred = rf_pred\n", "    best_model = rf_model\n", "elif best_model_name == 'Gradient Boosting':\n", "    best_pred = gb_pred\n", "    best_model = gb_model\n", "elif best_model_name == 'Linear Regression':\n", "    best_pred = lr_pred\n", "    best_model = lr_model\n", "elif best_model_name == 'Ridge':\n", "    best_pred = ridge_pred\n", "    best_model = ridge_model\n", "elif best_model_name == '<PERSON><PERSON>':\n", "    best_pred = lasso_pred\n", "    best_model = lasso_model\n", "else:\n", "    best_pred = svr_pred\n", "    best_model = svr_model\n", "\n", "residuals = y_test - best_pred\n", "\n", "# 残差分析图\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# 残差 vs 预测值\n", "axes[0,0].scatter(best_pred, residuals, alpha=0.5)\n", "axes[0,0].axhline(y=0, color='r', linestyle='--')\n", "axes[0,0].set_xlabel('预测值')\n", "axes[0,0].set_ylabel('残差')\n", "axes[0,0].set_title('残差 vs 预测值')\n", "\n", "# 残差直方图\n", "axes[0,1].hist(residuals, bins=50, alpha=0.7)\n", "axes[0,1].set_xlabel('残差')\n", "axes[0,1].set_ylabel('频次')\n", "axes[0,1].set_title('残差分布')\n", "\n", "# Q-Q图\n", "from scipy import stats\n", "stats.probplot(residuals, dist=\"norm\", plot=axes[1,0])\n", "axes[1,0].set_title('残差Q-Q图')\n", "\n", "# 残差 vs 实际值\n", "axes[1,1].scatter(y_test, residuals, alpha=0.5)\n", "axes[1,1].axhline(y=0, color='r', linestyle='--')\n", "axes[1,1].set_xlabel('实际值')\n", "axes[1,1].set_ylabel('残差')\n", "axes[1,1].set_title('残差 vs 实际值')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "bbce10f6593072d2", "metadata": {}, "outputs": [], "source": ["# ===== 11. 模型保存 =====\n", "print(\"\\n=== 模型保存 ===\")\n", "\n", "# 创建模型目录\n", "model_dir = 'model'\n", "if not os.path.exists(model_dir):\n", "    os.makedirs(model_dir)\n", "\n", "# 保存最佳模型\n", "best_model_path = os.path.join(model_dir, f'best_model_{best_model_name.lower().replace(\" \", \"_\")}.pkl')\n", "joblib.dump(best_model, best_model_path)\n", "print(f\"最佳模型已保存到: {best_model_path}\")\n", "\n", "# 保存标准化器\n", "scaler_path = os.path.join(model_dir, 'scaler.pkl')\n", "joblib.dump(scaler, scaler_path)\n", "print(f\"标准化器已保存到: {scaler_path}\")\n", "\n", "# 保存特征列名\n", "feature_path = os.path.join(model_dir, 'feature_columns.pkl')\n", "joblib.dump(feature_columns, feature_path)\n", "print(f\"特征列名已保存到: {feature_path}\")\n", "\n", "# 保存模型比较结果\n", "results_path = os.path.join(model_dir, 'model_comparison.csv')\n", "comparison_df.to_csv(results_path)\n", "print(f\"模型比较结果已保存到: {results_path}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "ac0ce4e7f6a8a44f", "metadata": {}, "outputs": [], "source": ["# ===== 12. 业务洞察和总结 =====\n", "print(\"\\n=== 业务洞察和总结 ===\")\n", "\n", "print(\"1. 数据质量:\")\n", "print(f\"   - 原始数据: {original_size:,} 条记录\")\n", "print(f\"   - 清理后数据: {len(df_clean):,} 条记录\")\n", "print(f\"   - 数据清理率: {(original_size - len(df_clean))/original_size*100:.1f}%\")\n", "\n", "print(\"\\n2. 模型性能:\")\n", "print(f\"   - 最佳模型: {best_model_name}\")\n", "print(f\"   - R²得分: {comparison_df.loc[best_model_name, 'R2']:.4f}\")\n", "print(f\"   - RMSE: ${comparison_df.loc[best_model_name, 'RMSE']:,.0f}\")\n", "print(f\"   - MAE: ${comparison_df.loc[best_model_name, 'MAE']:,.0f}\")\n", "\n", "print(\"\\n3. 关键特征 (基于随机森林):\")\n", "for i, row in rf_importance.head(5).iterrows():\n", "    print(f\"   - {row['特征']}: {row['重要性']:.3f}\")\n", "\n", "print(\"\\n4. 业务建议:\")\n", "print(\"   - 居住面积是影响房价的最重要因素\")\n", "print(\"   - 建筑等级和地理位置也显著影响房价\")\n", "print(\"   - 模型可用于房价估算，但需要考虑市场变化\")\n", "print(\"   - 建议定期更新模型以适应市场变化\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "dfbccfb1464ea1d0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 5}