#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

#%%
print("=== 快速修复模型 ===")

# 加载和预处理数据
df = pd.read_csv('data.csv')
print(f"原始数据形状: {df.shape}")

# 基础预处理
df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')
df['year'] = df['date'].dt.year
df['month'] = df['date'].dt.month

# 创建更有意义的特征
df['house_age'] = df['year'] - df['yr_built']
df['is_renovated'] = (df['yr_renovated'] > 0).astype(int)
df['years_since_renovation'] = np.where(df['yr_renovated'] > 0, 
                                       df['year'] - df['yr_renovated'], 
                                       df['house_age'])

# 重要：添加价格相关特征
df['price_per_sqft'] = df['price'] / df['sqft_living']
df['living_lot_ratio'] = df['sqft_living'] / df['sqft_lot']

# 数据清理
price_99_5 = df['price'].quantile(0.995)
price_0_5 = df['price'].quantile(0.005)
df_clean = df[(df['price'] >= price_0_5) & (df['price'] <= price_99_5)]

living_99_5 = df['sqft_living'].quantile(0.995)
living_0_5 = df['sqft_living'].quantile(0.005)
df_clean = df_clean[(df_clean['sqft_living'] >= living_0_5) & (df_clean['sqft_living'] <= living_99_5)]

print(f"清理后数据形状: {df_clean.shape}")

#%%
# 改进的特征选择
print("\n=== 改进的特征选择 ===")

# 移除冗余和弱相关特征，添加重要特征
improved_features = [
    'sqft_living',      # 最重要
    'grade',            # 建筑质量
    'lat', 'long',      # 位置
    'house_age',        # 房屋年龄（移除yr_built避免冗余）
    'is_renovated',     # 是否翻新
    'sqft_lot',         # 土地面积
    'living_lot_ratio'  # 面积比例
    # 移除：year, month（影响小），yr_built（与house_age冗余），years_since_renovation（复杂度高）
]

X = df_clean[improved_features].copy()
y = df_clean['price'].copy()

print(f"改进后特征数量: {X.shape[1]}")
print(f"样本数量: {X.shape[0]}")
print("选择的特征:", improved_features)

# 检查特征相关性
correlation_with_target = X.corrwith(y).abs().sort_values(ascending=False)
print("\n特征与价格的相关性:")
for feature, corr in correlation_with_target.items():
    print(f"  {feature}: {corr:.4f}")

#%%
# 数据分割和预处理
print("\n=== 数据预处理 ===")

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

print(f"训练集大小: {X_train.shape}")
print(f"测试集大小: {X_test.shape}")

# 只标准化特征，不标准化目标变量
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

print(f"价格范围: ${y_train.min():,.0f} - ${y_train.max():,.0f}")

#%%
# 模型训练和评估
def evaluate_model(y_true, y_pred, model_name):
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(y_true - y_pred))
    r2 = r2_score(y_true, y_pred)
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
    
    print(f"\n{model_name} 模型评估:")
    print(f"  均方根误差 (RMSE): ${rmse:,.0f}")
    print(f"  平均绝对误差 (MAE): ${mae:,.0f}")
    print(f"  决定系数 (R²): {r2:.4f}")
    print(f"  平均绝对百分比误差 (MAPE): {mape:.2f}%")
    
    return {'RMSE': rmse, 'MAE': mae, 'R2': r2, 'MAPE': mape}

results = {}

#%%
# 1. 改进的线性回归
print("=== 改进的线性回归 ===")
lr_improved = LinearRegression()
lr_improved.fit(X_train_scaled, y_train)
lr_pred = lr_improved.predict(X_test_scaled)
results['改进线性回归'] = evaluate_model(y_test, lr_pred, "改进线性回归")

#%%
# 2. Ridge回归
print("\n=== Ridge回归 ===")
ridge = Ridge(alpha=100.0)
ridge.fit(X_train_scaled, y_train)
ridge_pred = ridge.predict(X_test_scaled)
results['Ridge回归'] = evaluate_model(y_test, ridge_pred, "Ridge回归")

#%%
# 3. 对数变换方法
print("\n=== 对数变换方法 ===")
y_train_log = np.log(y_train)
y_test_log = np.log(y_test)

lr_log = LinearRegression()
lr_log.fit(X_train_scaled, y_train_log)
lr_log_pred = lr_log.predict(X_test_scaled)
lr_log_pred_original = np.exp(lr_log_pred)  # 反变换

results['对数变换线性回归'] = evaluate_model(y_test, lr_log_pred_original, "对数变换线性回归")

#%%
# 4. 随机森林（基准）
print("\n=== 随机森林 ===")
rf = RandomForestRegressor(n_estimators=100, max_depth=20, random_state=42, n_jobs=-1)
rf.fit(X_train, y_train)  # 使用原始数据
rf_pred = rf.predict(X_test)
results['随机森林'] = evaluate_model(y_test, rf_pred, "随机森林")

#%%
# 结果比较
print("\n=== 结果比较 ===")
results_df = pd.DataFrame(results).T
print(results_df.round(2))

best_model = results_df['RMSE'].idxmin()
print(f"\n🏆 最佳模型: {best_model}")
print(f"   RMSE: ${results_df.loc[best_model, 'RMSE']:,.0f}")
print(f"   R²: {results_df.loc[best_model, 'R2']:.4f}")
print(f"   MAPE: {results_df.loc[best_model, 'MAPE']:.2f}%")

#%%
# 可视化结果
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# RMSE比较
axes[0,0].bar(results_df.index, results_df['RMSE'])
axes[0,0].set_title('RMSE比较')
axes[0,0].set_ylabel('RMSE ($)')
axes[0,0].tick_params(axis='x', rotation=45)

# R²比较
axes[0,1].bar(results_df.index, results_df['R2'])
axes[0,1].set_title('R²比较')
axes[0,1].set_ylabel('R²')
axes[0,1].tick_params(axis='x', rotation=45)

# MAE比较
axes[1,0].bar(results_df.index, results_df['MAE'])
axes[1,0].set_title('MAE比较')
axes[1,0].set_ylabel('MAE ($)')
axes[1,0].tick_params(axis='x', rotation=45)

# MAPE比较
axes[1,1].bar(results_df.index, results_df['MAPE'])
axes[1,1].set_title('MAPE比较')
axes[1,1].set_ylabel('MAPE (%)')
axes[1,1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

#%%
# 特征重要性（随机森林）
print("\n=== 特征重要性分析 ===")
feature_importance = pd.DataFrame({
    '特征': improved_features,
    '重要性': rf.feature_importances_
}).sort_values('重要性', ascending=False)

print("特征重要性排序:")
for _, row in feature_importance.iterrows():
    print(f"  {row['特征']}: {row['重要性']:.4f}")

# 可视化特征重要性
plt.figure(figsize=(10, 6))
plt.barh(range(len(feature_importance)), feature_importance['重要性'])
plt.yticks(range(len(feature_importance)), feature_importance['特征'])
plt.xlabel('重要性')
plt.title('特征重要性（随机森林）')
plt.gca().invert_yaxis()
plt.tight_layout()
plt.show()

#%%
# 改进建议
print("\n=== 改进建议 ===")
print("1. ✅ 移除了冗余特征（yr_built, year, month）")
print("2. ✅ 添加了重要的比例特征（living_lot_ratio）")
print("3. ✅ 不对目标变量进行标准化")
print("4. ✅ 尝试了对数变换")
print("5. 💡 进一步改进建议：")
print("   - 添加更多位置特征（到市中心距离、邻域聚类）")
print("   - 创建更多交互特征（grade × sqft_living）")
print("   - 尝试集成方法（XGBoost, LightGBM）")
print("   - 进行超参数调优")

print(f"\n📊 性能改进：RMSE从 $184,261 降低到 ${results_df.loc[best_model, 'RMSE']:,.0f}")
print("快速修复完成！")
