#%%
# 测试环境和依赖包
import sys
print("Python版本:", sys.version)

try:
    import pandas as pd
    print("✓ pandas版本:", pd.__version__)
except ImportError:
    print("✗ pandas未安装")

try:
    import numpy as np
    print("✓ numpy版本:", np.__version__)
except ImportError:
    print("✗ numpy未安装")

try:
    import matplotlib.pyplot as plt
    print("✓ matplotlib可用")
except ImportError:
    print("✗ matplotlib未安装")

try:
    import seaborn as sns
    print("✓ seaborn可用")
except ImportError:
    print("✗ seaborn未安装")

try:
    import sklearn
    print("✓ scikit-learn版本:", sklearn.__version__)
except ImportError:
    print("✗ scikit-learn未安装")

try:
    import joblib
    print("✓ joblib可用")
except ImportError:
    print("✗ joblib未安装")

try:
    from scipy import stats
    print("✓ scipy可用")
except ImportError:
    print("✗ scipy未安装")

#%%
# 测试数据文件是否存在
import os
if os.path.exists('data.csv'):
    print("✓ data.csv文件存在")
    # 快速检查数据
    df = pd.read_csv('data.csv')
    print(f"数据形状: {df.shape}")
    print("列名:", df.columns.tolist())
else:
    print("✗ data.csv文件不存在")

print("\n环境检查完成！")
