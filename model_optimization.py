#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.feature_selection import SelectKBest, f_regression
import warnings
warnings.filterwarnings('ignore')

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

#%%
print("=== 模型优化和诊断 ===")

# 加载和预处理数据
df = pd.read_csv('data.csv')
print(f"原始数据形状: {df.shape}")

# 数据预处理
df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')
df['year'] = df['date'].dt.year
df['month'] = df['date'].dt.month
df['day_of_year'] = df['date'].dt.dayofyear

# 处理翻新
df['is_renovated'] = (df['yr_renovated'] > 0).astype(int)
df['years_since_renovation'] = np.where(df['yr_renovated'] > 0, 
                                       df['year'] - df['yr_renovated'], 
                                       df['year'] - df['yr_built'])

# 房屋年龄
df['house_age'] = df['year'] - df['yr_built']

# 价格每平方英尺
df['price_per_sqft'] = df['price'] / df['sqft_living']

# 面积比例
df['living_lot_ratio'] = df['sqft_living'] / df['sqft_lot']

# 数据清理（移除极端异常值）
price_99_5 = df['price'].quantile(0.995)
price_0_5 = df['price'].quantile(0.005)
df_clean = df[(df['price'] >= price_0_5) & (df['price'] <= price_99_5)]

living_99_5 = df['sqft_living'].quantile(0.995)
living_0_5 = df['sqft_living'].quantile(0.005)
df_clean = df_clean[(df_clean['sqft_living'] >= living_0_5) & (df_clean['sqft_living'] <= living_99_5)]

print(f"清理后数据形状: {df_clean.shape}")

#%%
# 特征工程和选择
print("\n=== 特征工程和选择 ===")

# 扩展特征集
feature_columns = [
    'sqft_lot', 'sqft_living', 'grade', 'lat', 'long', 
    'yr_built', 'house_age', 'is_renovated', 'years_since_renovation',
    'year', 'month', 'price_per_sqft', 'living_lot_ratio'
]

X = df_clean[feature_columns].copy()
y = df_clean['price'].copy()

# 检查特征与目标变量的相关性
correlation_with_target = X.corrwith(y).abs().sort_values(ascending=False)
print("特征与价格的相关性:")
print(correlation_with_target)

#%%
# 使用不同的标准化方法
print("\n=== 测试不同的标准化方法 ===")

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 1. StandardScaler
scaler_std = StandardScaler()
X_train_std = scaler_std.fit_transform(X_train)
X_test_std = scaler_std.transform(X_test)

y_scaler_std = StandardScaler()
y_train_std = y_scaler_std.fit_transform(y_train.values.reshape(-1, 1)).ravel()
y_test_std = y_scaler_std.transform(y_test.values.reshape(-1, 1)).ravel()

# 2. RobustScaler (对异常值更鲁棒)
scaler_robust = RobustScaler()
X_train_robust = scaler_robust.fit_transform(X_train)
X_test_robust = scaler_robust.transform(X_test)

y_scaler_robust = RobustScaler()
y_train_robust = y_scaler_robust.fit_transform(y_train.values.reshape(-1, 1)).ravel()
y_test_robust = y_scaler_robust.transform(y_test.values.reshape(-1, 1)).ravel()

# 3. MinMaxScaler
scaler_minmax = MinMaxScaler()
X_train_minmax = scaler_minmax.fit_transform(X_train)
X_test_minmax = scaler_minmax.transform(X_test)

y_scaler_minmax = MinMaxScaler()
y_train_minmax = y_scaler_minmax.fit_transform(y_train.values.reshape(-1, 1)).ravel()
y_test_minmax = y_scaler_minmax.transform(y_test.values.reshape(-1, 1)).ravel()

#%%
# 评估函数
def evaluate_model_detailed(y_true, y_pred, scaler, model_name, scaled_data=True):
    """详细评估模型性能"""
    if scaled_data:
        y_true_orig = scaler.inverse_transform(y_true.reshape(-1, 1)).ravel()
        y_pred_orig = scaler.inverse_transform(y_pred.reshape(-1, 1)).ravel()
    else:
        y_true_orig = y_true
        y_pred_orig = y_pred
    
    mse = mean_squared_error(y_true_orig, y_pred_orig)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true_orig, y_pred_orig)
    r2 = r2_score(y_true_orig, y_pred_orig)
    
    # 计算相对误差
    mape = np.mean(np.abs((y_true_orig - y_pred_orig) / y_true_orig)) * 100
    
    print(f"\n{model_name}:")
    print(f"  RMSE: ${rmse:,.0f}")
    print(f"  MAE: ${mae:,.0f}")
    print(f"  R²: {r2:.4f}")
    print(f"  MAPE: {mape:.2f}%")
    
    return {'RMSE': rmse, 'MAE': mae, 'R2': r2, 'MAPE': mape}

#%%
# 测试不同标准化方法的效果
print("\n=== 比较不同标准化方法 ===")

results = {}

# 使用线性回归测试不同标准化方法
for scaler_name, (X_tr, X_te, y_tr, y_te, y_scaler) in [
    ('StandardScaler', (X_train_std, X_test_std, y_train_std, y_test_std, y_scaler_std)),
    ('RobustScaler', (X_train_robust, X_test_robust, y_train_robust, y_test_robust, y_scaler_robust)),
    ('MinMaxScaler', (X_train_minmax, X_test_minmax, y_train_minmax, y_test_minmax, y_scaler_minmax))
]:
    lr = LinearRegression()
    lr.fit(X_tr, y_tr)
    pred = lr.predict(X_te)
    results[f'LinearRegression_{scaler_name}'] = evaluate_model_detailed(
        y_te, pred, y_scaler, f"线性回归 + {scaler_name}", scaled_data=True
    )

#%%
# 特征选择
print("\n=== 特征选择优化 ===")

# 使用SelectKBest选择最重要的特征
selector = SelectKBest(score_func=f_regression, k=8)  # 选择前8个最重要的特征
X_train_selected = selector.fit_transform(X_train_std, y_train_std)
X_test_selected = selector.transform(X_test_std)

# 获取选择的特征名
selected_features = [feature_columns[i] for i in selector.get_support(indices=True)]
print(f"选择的特征: {selected_features}")

# 使用选择的特征训练模型
lr_selected = LinearRegression()
lr_selected.fit(X_train_selected, y_train_std)
pred_selected = lr_selected.predict(X_test_selected)
results['LinearRegression_FeatureSelected'] = evaluate_model_detailed(
    y_test_std, pred_selected, y_scaler_std, "线性回归 + 特征选择", scaled_data=True
)

#%%
# 超参数调优
print("\n=== 超参数调优 ===")

# Ridge回归调优
ridge_params = {'alpha': [0.1, 1.0, 10.0, 100.0, 1000.0]}
ridge_grid = GridSearchCV(Ridge(random_state=42), ridge_params, cv=5, scoring='r2')
ridge_grid.fit(X_train_std, y_train_std)
print(f"Ridge最佳参数: {ridge_grid.best_params_}")

ridge_best = ridge_grid.best_estimator_
ridge_pred = ridge_best.predict(X_test_std)
results['Ridge_Optimized'] = evaluate_model_detailed(
    y_test_std, ridge_pred, y_scaler_std, "优化后的Ridge回归", scaled_data=True
)

# Lasso回归调优
lasso_params = {'alpha': [0.01, 0.1, 1.0, 10.0, 100.0]}
lasso_grid = GridSearchCV(Lasso(random_state=42), lasso_params, cv=5, scoring='r2')
lasso_grid.fit(X_train_std, y_train_std)
print(f"Lasso最佳参数: {lasso_grid.best_params_}")

lasso_best = lasso_grid.best_estimator_
lasso_pred = lasso_best.predict(X_test_std)
results['Lasso_Optimized'] = evaluate_model_detailed(
    y_test_std, lasso_pred, y_scaler_std, "优化后的Lasso回归", scaled_data=True
)

#%%
# 随机森林调优（使用原始数据）
print("\n=== 树模型优化 ===")

rf_params = {
    'n_estimators': [100, 200],
    'max_depth': [10, 20, None],
    'min_samples_split': [2, 5],
    'min_samples_leaf': [1, 2]
}

rf_grid = GridSearchCV(RandomForestRegressor(random_state=42), rf_params, cv=3, scoring='r2', n_jobs=-1)
rf_grid.fit(X_train, y_train)
print(f"随机森林最佳参数: {rf_grid.best_params_}")

rf_best = rf_grid.best_estimator_
rf_pred = rf_best.predict(X_test)
results['RandomForest_Optimized'] = evaluate_model_detailed(
    y_test, rf_pred, None, "优化后的随机森林", scaled_data=False
)

#%%
# 结果比较
print("\n=== 模型性能比较 ===")
comparison_df = pd.DataFrame(results).T
comparison_df = comparison_df.round(2)
print(comparison_df)

# 找出最佳模型
best_model = comparison_df['R2'].idxmax()
print(f"\n最佳模型: {best_model}")
print(f"R²: {comparison_df.loc[best_model, 'R2']:.4f}")
print(f"RMSE: ${comparison_df.loc[best_model, 'RMSE']:,.0f}")

#%%
# 可视化结果
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# R²比较
axes[0,0].bar(range(len(comparison_df)), comparison_df['R2'])
axes[0,0].set_title('R²比较')
axes[0,0].set_ylabel('R²')
axes[0,0].set_xticks(range(len(comparison_df)))
axes[0,0].set_xticklabels(comparison_df.index, rotation=45, ha='right')

# RMSE比较
axes[0,1].bar(range(len(comparison_df)), comparison_df['RMSE'])
axes[0,1].set_title('RMSE比较')
axes[0,1].set_ylabel('RMSE ($)')
axes[0,1].set_xticks(range(len(comparison_df)))
axes[0,1].set_xticklabels(comparison_df.index, rotation=45, ha='right')

# MAE比较
axes[1,0].bar(range(len(comparison_df)), comparison_df['MAE'])
axes[1,0].set_title('MAE比较')
axes[1,0].set_ylabel('MAE ($)')
axes[1,0].set_xticks(range(len(comparison_df)))
axes[1,0].set_xticklabels(comparison_df.index, rotation=45, ha='right')

# MAPE比较
axes[1,1].bar(range(len(comparison_df)), comparison_df['MAPE'])
axes[1,1].set_title('MAPE比较')
axes[1,1].set_ylabel('MAPE (%)')
axes[1,1].set_xticks(range(len(comparison_df)))
axes[1,1].set_xticklabels(comparison_df.index, rotation=45, ha='right')

plt.tight_layout()
plt.show()

print("\n模型优化完成！")
