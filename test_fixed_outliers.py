#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示为方块的问题

#%%
# 加载数据
df = pd.read_csv('data.csv')
print(f"数据形状: {df.shape}")

#%%
# 修复后的异常值检测函数
def detect_outliers_iqr(data, column):
    """使用改进的IQR方法检测异常值"""
    Q1 = data[column].quantile(0.25)
    Q3 = data[column].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    
    # 对于价格和面积数据，下界不能为负值
    if column in ['price', 'sqft_living', 'sqft_lot']:
        lower_bound = max(0, lower_bound)
    
    outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]
    return outliers, lower_bound, upper_bound

#%%
print("=== 修复后的异常值检测 ===")

# 检测价格异常值
price_outliers, price_lower, price_upper = detect_outliers_iqr(df, 'price')
print(f"价格异常值数量: {len(price_outliers)} ({len(price_outliers)/len(df)*100:.2f}%)")
print(f"价格正常范围: ${price_lower:,.0f} - ${price_upper:,.0f}")

# 检测居住面积异常值
living_outliers, living_lower, living_upper = detect_outliers_iqr(df, 'sqft_living')
print(f"居住面积异常值数量: {len(living_outliers)} ({len(living_outliers)/len(df)*100:.2f}%)")
print(f"居住面积正常范围: {living_lower:,.0f} - {living_upper:,.0f} sqft")

# 检测土地面积异常值
lot_outliers, lot_lower, lot_upper = detect_outliers_iqr(df, 'sqft_lot')
print(f"土地面积异常值数量: {len(lot_outliers)} ({len(lot_outliers)/len(df)*100:.2f}%)")
print(f"土地面积正常范围: {lot_lower:,.0f} - {lot_upper:,.0f} sqft")

# 检测经度异常值（应该允许负值）
long_outliers, long_lower, long_upper = detect_outliers_iqr(df, 'long')
print(f"经度异常值数量: {len(long_outliers)} ({len(long_outliers)/len(df)*100:.2f}%)")
print(f"经度正常范围: {long_lower:.3f} - {long_upper:.3f}")

# 检测纬度异常值
lat_outliers, lat_lower, lat_upper = detect_outliers_iqr(df, 'lat')
print(f"纬度异常值数量: {len(lat_outliers)} ({len(lat_outliers)/len(df)*100:.2f}%)")
print(f"纬度正常范围: {lat_lower:.3f} - {lat_upper:.3f}")

#%%
print("\n=== 数据统计验证 ===")
print(f"价格统计:")
print(f"  最小值: ${df['price'].min():,.0f}")
print(f"  最大值: ${df['price'].max():,.0f}")
print(f"  中位数: ${df['price'].median():,.0f}")
print(f"  平均值: ${df['price'].mean():,.0f}")
print(f"  Q1: ${df['price'].quantile(0.25):,.0f}")
print(f"  Q3: ${df['price'].quantile(0.75):,.0f}")

print(f"\n居住面积统计:")
print(f"  最小值: {df['sqft_living'].min():,.0f} sqft")
print(f"  最大值: {df['sqft_living'].max():,.0f} sqft")
print(f"  中位数: {df['sqft_living'].median():,.0f} sqft")
print(f"  平均值: {df['sqft_living'].mean():,.0f} sqft")
print(f"  Q1: {df['sqft_living'].quantile(0.25):,.0f} sqft")
print(f"  Q3: {df['sqft_living'].quantile(0.75):,.0f} sqft")

print(f"\n经度统计:")
print(f"  最小值: {df['long'].min():.3f}")
print(f"  最大值: {df['long'].max():.3f}")
print(f"  中位数: {df['long'].median():.3f}")
print(f"  平均值: {df['long'].mean():.3f}")

#%%
# 可视化修复后的结果
fig, axes = plt.subplots(2, 3, figsize=(18, 12))

# 价格分布和异常值
axes[0,0].hist(df['price'], bins=100, alpha=0.7, label='所有数据')
if len(price_outliers) > 0:
    axes[0,0].hist(price_outliers['price'], bins=50, alpha=0.7, color='red', label='异常值')
axes[0,0].axvline(price_lower, color='green', linestyle='--', label=f'下界: ${price_lower:,.0f}')
axes[0,0].axvline(price_upper, color='green', linestyle='--', label=f'上界: ${price_upper:,.0f}')
axes[0,0].set_title('价格分布与异常值')
axes[0,0].set_xlabel('价格 ($)')
axes[0,0].legend()

# 居住面积分布和异常值
axes[0,1].hist(df['sqft_living'], bins=100, alpha=0.7, label='所有数据')
if len(living_outliers) > 0:
    axes[0,1].hist(living_outliers['sqft_living'], bins=50, alpha=0.7, color='red', label='异常值')
axes[0,1].axvline(living_lower, color='green', linestyle='--', label=f'下界: {living_lower:,.0f}')
axes[0,1].axvline(living_upper, color='green', linestyle='--', label=f'上界: {living_upper:,.0f}')
axes[0,1].set_title('居住面积分布与异常值')
axes[0,1].set_xlabel('面积 (sqft)')
axes[0,1].legend()

# 土地面积分布和异常值
axes[0,2].hist(df['sqft_lot'], bins=100, alpha=0.7, label='所有数据')
if len(lot_outliers) > 0:
    axes[0,2].hist(lot_outliers['sqft_lot'], bins=50, alpha=0.7, color='red', label='异常值')
axes[0,2].axvline(lot_lower, color='green', linestyle='--', label=f'下界: {lot_lower:,.0f}')
axes[0,2].axvline(lot_upper, color='green', linestyle='--', label=f'上界: {lot_upper:,.0f}')
axes[0,2].set_title('土地面积分布与异常值')
axes[0,2].set_xlabel('面积 (sqft)')
axes[0,2].legend()

# 经度分布和异常值
axes[1,0].hist(df['long'], bins=100, alpha=0.7, label='所有数据')
if len(long_outliers) > 0:
    axes[1,0].hist(long_outliers['long'], bins=50, alpha=0.7, color='red', label='异常值')
axes[1,0].axvline(long_lower, color='green', linestyle='--', label=f'下界: {long_lower:.3f}')
axes[1,0].axvline(long_upper, color='green', linestyle='--', label=f'上界: {long_upper:.3f}')
axes[1,0].set_title('经度分布与异常值')
axes[1,0].set_xlabel('经度')
axes[1,0].legend()

# 纬度分布和异常值
axes[1,1].hist(df['lat'], bins=100, alpha=0.7, label='所有数据')
if len(lat_outliers) > 0:
    axes[1,1].hist(lat_outliers['lat'], bins=50, alpha=0.7, color='red', label='异常值')
axes[1,1].axvline(lat_lower, color='green', linestyle='--', label=f'下界: {lat_lower:.3f}')
axes[1,1].axvline(lat_upper, color='green', linestyle='--', label=f'上界: {lat_upper:.3f}')
axes[1,1].set_title('纬度分布与异常值')
axes[1,1].set_xlabel('纬度')
axes[1,1].legend()

# 地理位置散点图
scatter = axes[1,2].scatter(df['long'], df['lat'], c=df['price'], alpha=0.6, s=1, cmap='viridis')
axes[1,2].set_title('房屋地理位置分布（颜色表示价格）')
axes[1,2].set_xlabel('经度')
axes[1,2].set_ylabel('纬度')
plt.colorbar(scatter, ax=axes[1,2], label='价格 ($)')

plt.tight_layout()
plt.show()

print("\n异常值检测修复验证完成！")
