#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

#%%
print("=== 高级特征工程 ===")

# 加载数据
df = pd.read_csv('data.csv')
print(f"原始数据形状: {df.shape}")

# 基础预处理
df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')
df['year'] = df['date'].dt.year
df['month'] = df['date'].dt.month

# 数据清理
price_99_5 = df['price'].quantile(0.995)
price_0_5 = df['price'].quantile(0.005)
df_clean = df[(df['price'] >= price_0_5) & (df['price'] <= price_99_5)]

living_99_5 = df['sqft_living'].quantile(0.995)
living_0_5 = df['sqft_living'].quantile(0.005)
df_clean = df_clean[(df_clean['sqft_living'] >= living_0_5) & (df_clean['sqft_living'] <= living_99_5)]

print(f"清理后数据形状: {df_clean.shape}")

#%%
# 高级特征工程
print("\n=== 创建高级特征 ===")

# 1. 基础特征
df_clean['house_age'] = df_clean['year'] - df_clean['yr_built']
df_clean['is_renovated'] = (df_clean['yr_renovated'] > 0).astype(int)
df_clean['years_since_renovation'] = np.where(
    df_clean['yr_renovated'] > 0, 
    df_clean['year'] - df_clean['yr_renovated'], 
    df_clean['house_age']
)

# 2. 面积相关特征
df_clean['price_per_sqft'] = df_clean['price'] / df_clean['sqft_living']
df_clean['living_lot_ratio'] = df_clean['sqft_living'] / df_clean['sqft_lot']
df_clean['log_sqft_living'] = np.log1p(df_clean['sqft_living'])
df_clean['log_sqft_lot'] = np.log1p(df_clean['sqft_lot'])
df_clean['sqrt_sqft_living'] = np.sqrt(df_clean['sqft_living'])

# 3. 位置相关特征
# 计算到市中心的距离（假设西雅图市中心）
seattle_center_lat = 47.6062
seattle_center_long = -122.3321

df_clean['distance_to_center'] = np.sqrt(
    (df_clean['lat'] - seattle_center_lat)**2 + 
    (df_clean['long'] - seattle_center_long)**2
)

# 位置聚类特征
df_clean['lat_binned'] = pd.cut(df_clean['lat'], bins=10, labels=False)
df_clean['long_binned'] = pd.cut(df_clean['long'], bins=10, labels=False)
df_clean['location_cluster'] = df_clean['lat_binned'] * 10 + df_clean['long_binned']

# 4. 等级相关特征
df_clean['grade_squared'] = df_clean['grade'] ** 2
df_clean['is_high_grade'] = (df_clean['grade'] >= 8).astype(int)

# 5. 时间相关特征
df_clean['is_summer'] = df_clean['month'].isin([6, 7, 8]).astype(int)
df_clean['is_spring'] = df_clean['month'].isin([3, 4, 5]).astype(int)
df_clean['quarter'] = df_clean['month'].apply(lambda x: (x-1)//3 + 1)

# 6. 交互特征
df_clean['grade_x_living'] = df_clean['grade'] * df_clean['sqft_living']
df_clean['age_x_grade'] = df_clean['house_age'] * df_clean['grade']
df_clean['living_x_lot'] = df_clean['sqft_living'] * df_clean['sqft_lot']

# 7. 分箱特征
df_clean['living_size_category'] = pd.cut(
    df_clean['sqft_living'], 
    bins=[0, 1000, 2000, 3000, float('inf')], 
    labels=['小', '中', '大', '超大']
)
df_clean['living_size_small'] = (df_clean['living_size_category'] == '小').astype(int)
df_clean['living_size_medium'] = (df_clean['living_size_category'] == '中').astype(int)
df_clean['living_size_large'] = (df_clean['living_size_category'] == '大').astype(int)
df_clean['living_size_xlarge'] = (df_clean['living_size_category'] == '超大').astype(int)

print("创建的新特征数量:", len([col for col in df_clean.columns if col not in df.columns]))

#%%
# 特征选择
print("\n=== 特征选择 ===")

# 选择数值特征
numeric_features = [
    'sqft_lot', 'sqft_living', 'grade', 'lat', 'long', 'yr_built',
    'house_age', 'is_renovated', 'years_since_renovation', 'year', 'month',
    'log_sqft_living', 'log_sqft_lot', 'sqrt_sqft_living',
    'distance_to_center', 'lat_binned', 'long_binned', 'location_cluster',
    'grade_squared', 'is_high_grade', 'is_summer', 'is_spring', 'quarter',
    'grade_x_living', 'age_x_grade', 'living_x_lot',
    'living_size_small', 'living_size_medium', 'living_size_large', 'living_size_xlarge'
]

# 检查特征是否存在
available_features = [f for f in numeric_features if f in df_clean.columns]
print(f"可用特征数量: {len(available_features)}")

X = df_clean[available_features].copy()
y = df_clean['price'].copy()

# 检查特征与目标的相关性
correlation_with_target = X.corrwith(y).abs().sort_values(ascending=False)
print("\n特征与价格的相关性（前10）:")
print(correlation_with_target.head(10))

#%%
# 模型训练和比较
print("\n=== 模型训练和比较 ===")

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 标准化
scaler_X = StandardScaler()
X_train_scaled = scaler_X.fit_transform(X_train)
X_test_scaled = scaler_X.transform(X_test)

scaler_y = StandardScaler()
y_train_scaled = scaler_y.fit_transform(y_train.values.reshape(-1, 1)).ravel()
y_test_scaled = scaler_y.transform(y_test.values.reshape(-1, 1)).ravel()

def evaluate_model(y_true, y_pred, scaler, model_name, scaled=True):
    if scaled:
        y_true_orig = scaler.inverse_transform(y_true.reshape(-1, 1)).ravel()
        y_pred_orig = scaler.inverse_transform(y_pred.reshape(-1, 1)).ravel()
    else:
        y_true_orig = y_true
        y_pred_orig = y_pred
    
    rmse = np.sqrt(mean_squared_error(y_true_orig, y_pred_orig))
    r2 = r2_score(y_true_orig, y_pred_orig)
    
    print(f"{model_name}: RMSE=${rmse:,.0f}, R²={r2:.4f}")
    return rmse, r2

results = {}

# 1. 线性回归（标准化）
lr = LinearRegression()
lr.fit(X_train_scaled, y_train_scaled)
lr_pred = lr.predict(X_test_scaled)
rmse, r2 = evaluate_model(y_test_scaled, lr_pred, scaler_y, "线性回归（高级特征）", scaled=True)
results['LinearRegression_Advanced'] = {'RMSE': rmse, 'R2': r2}

# 2. Ridge回归（标准化）
ridge = Ridge(alpha=10.0)
ridge.fit(X_train_scaled, y_train_scaled)
ridge_pred = ridge.predict(X_test_scaled)
rmse, r2 = evaluate_model(y_test_scaled, ridge_pred, scaler_y, "Ridge回归（高级特征）", scaled=True)
results['Ridge_Advanced'] = {'RMSE': rmse, 'R2': r2}

# 3. 随机森林（原始数据）
rf = RandomForestRegressor(n_estimators=200, max_depth=20, random_state=42, n_jobs=-1)
rf.fit(X_train, y_train)
rf_pred = rf.predict(X_test)
rmse, r2 = evaluate_model(y_test, rf_pred, None, "随机森林（高级特征）", scaled=False)
results['RandomForest_Advanced'] = {'RMSE': rmse, 'R2': r2}

#%%
# 多项式特征
print("\n=== 多项式特征 ===")

# 选择最重要的几个特征进行多项式扩展
important_features = ['sqft_living', 'grade', 'lat', 'long', 'house_age']
X_important = X_train[important_features]
X_test_important = X_test[important_features]

# 创建二次多项式特征
poly = PolynomialFeatures(degree=2, interaction_only=True, include_bias=False)
X_train_poly = poly.fit_transform(X_important)
X_test_poly = poly.transform(X_test_important)

print(f"多项式特征数量: {X_train_poly.shape[1]}")

# 标准化多项式特征
scaler_poly = StandardScaler()
X_train_poly_scaled = scaler_poly.fit_transform(X_train_poly)
X_test_poly_scaled = scaler_poly.transform(X_test_poly)

# 训练Ridge回归（防止过拟合）
ridge_poly = Ridge(alpha=100.0)
ridge_poly.fit(X_train_poly_scaled, y_train_scaled)
ridge_poly_pred = ridge_poly.predict(X_test_poly_scaled)
rmse, r2 = evaluate_model(y_test_scaled, ridge_poly_pred, scaler_y, "Ridge回归（多项式特征）", scaled=True)
results['Ridge_Polynomial'] = {'RMSE': rmse, 'R2': r2}

#%%
# 特征重要性分析
print("\n=== 特征重要性分析 ===")

# 随机森林特征重要性
feature_importance = pd.DataFrame({
    '特征': available_features,
    '重要性': rf.feature_importances_
}).sort_values('重要性', ascending=False)

print("前15个重要特征:")
print(feature_importance.head(15))

# 可视化特征重要性
plt.figure(figsize=(12, 8))
top_features = feature_importance.head(15)
plt.barh(range(len(top_features)), top_features['重要性'])
plt.yticks(range(len(top_features)), top_features['特征'])
plt.xlabel('重要性')
plt.title('特征重要性（随机森林）')
plt.gca().invert_yaxis()
plt.tight_layout()
plt.show()

#%%
# 结果总结
print("\n=== 最终结果比较 ===")
results_df = pd.DataFrame(results).T
print(results_df)

best_model = results_df['R2'].idxmax()
print(f"\n最佳模型: {best_model}")
print(f"R²: {results_df.loc[best_model, 'R2']:.4f}")
print(f"RMSE: ${results_df.loc[best_model, 'RMSE']:,.0f}")

# 可视化结果
fig, axes = plt.subplots(1, 2, figsize=(15, 6))

axes[0].bar(results_df.index, results_df['R2'])
axes[0].set_title('R²比较')
axes[0].set_ylabel('R²')
axes[0].tick_params(axis='x', rotation=45)

axes[1].bar(results_df.index, results_df['RMSE'])
axes[1].set_title('RMSE比较')
axes[1].set_ylabel('RMSE ($)')
axes[1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

print("\n高级特征工程完成！")
