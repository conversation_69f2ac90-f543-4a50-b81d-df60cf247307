#%%
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime
import os

warnings.filterwarnings('ignore')

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示为方块的问题

#%%
# ===== 1. 数据加载和初步探索 =====
print("=== 数据加载 ===")
df = pd.read_csv('data.csv')
print(f"数据集形状: {df.shape}")
print(f"数据集列名: {df.columns.tolist()}")
print("\n数据集前5行:")
print(df.head())

#%%
# 数据基本信息
print("\n=== 数据基本信息 ===")
print(df.info())
print("\n数据统计描述:")
print(df.describe())

#%%
# ===== 2. 数据质量检查 =====
print("\n=== 缺失值检查 ===")
missing_values = df.isnull().sum()
print("各列缺失值数量:")
print(missing_values)
print(f"\n总缺失值数量: {missing_values.sum()}")
print(f"缺失值比例: {(missing_values.sum() / (df.shape[0] * df.shape[1])) * 100:.2f}%")

#%%
# 检查重复值
print("\n=== 重复值检查 ===")
duplicates = df.duplicated().sum()
print(f"重复行数量: {duplicates}")
print(f"重复行比例: {(duplicates / len(df)) * 100:.2f}%")

#%%
# ===== 3. 数据预处理和特征工程 =====
print("\n=== 数据预处理 ===")

# 处理日期列
df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')
df['year'] = df['date'].dt.year
df['month'] = df['date'].dt.month
df['day_of_year'] = df['date'].dt.dayofyear

# 处理yr_renovated列（0表示未翻新）
df['is_renovated'] = (df['yr_renovated'] > 0).astype(int)
df['years_since_renovation'] = np.where(df['yr_renovated'] > 0, 
                                       df['year'] - df['yr_renovated'], 
                                       df['year'] - df['yr_built'])

# 房屋年龄
df['house_age'] = df['year'] - df['yr_built']

# 价格每平方英尺
df['price_per_sqft'] = df['price'] / df['sqft_living']

print("新特征创建完成")
print(f"处理后数据形状: {df.shape}")

#%%
# ===== 4. 异常值检测和处理 =====
print("\n=== 异常值检测 ===")

def detect_outliers_iqr(data, column):
    """使用IQR方法检测异常值"""
    Q1 = data[column].quantile(0.25)
    Q3 = data[column].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]
    return outliers, lower_bound, upper_bound

# 检测价格异常值
price_outliers, price_lower, price_upper = detect_outliers_iqr(df, 'price')
print(f"价格异常值数量: {len(price_outliers)} ({len(price_outliers)/len(df)*100:.2f}%)")
print(f"价格正常范围: ${price_lower:,.0f} - ${price_upper:,.0f}")

# 检测面积异常值
living_outliers, living_lower, living_upper = detect_outliers_iqr(df, 'sqft_living')
print(f"居住面积异常值数量: {len(living_outliers)} ({len(living_outliers)/len(df)*100:.2f}%)")

#%%
# 可视化异常值
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# 价格分布
axes[0,0].hist(df['price'], bins=50, alpha=0.7)
axes[0,0].set_title('房价分布')
axes[0,0].set_xlabel('价格 ($)')
axes[0,0].set_ylabel('频次')

# 价格箱线图
axes[0,1].boxplot(df['price'])
axes[0,1].set_title('房价箱线图')
axes[0,1].set_ylabel('价格 ($)')

# 居住面积分布
axes[1,0].hist(df['sqft_living'], bins=50, alpha=0.7)
axes[1,0].set_title('居住面积分布')
axes[1,0].set_xlabel('面积 (sqft)')
axes[1,0].set_ylabel('频次')

# 居住面积箱线图
axes[1,1].boxplot(df['sqft_living'])
axes[1,1].set_title('居住面积箱线图')
axes[1,1].set_ylabel('面积 (sqft)')

plt.tight_layout()
plt.show()

#%%
# 处理极端异常值（保留合理范围内的数据）
print("\n=== 异常值处理 ===")
original_size = len(df)

# 移除极端价格异常值（保留99%的数据）
price_99th = df['price'].quantile(0.99)
price_1st = df['price'].quantile(0.01)
df_clean = df[(df['price'] >= price_1st) & (df['price'] <= price_99th)]

# 移除极端面积异常值
living_99th = df['sqft_living'].quantile(0.99)
living_1st = df['sqft_living'].quantile(0.01)
df_clean = df_clean[(df_clean['sqft_living'] >= living_1st) & (df_clean['sqft_living'] <= living_99th)]

print(f"原始数据量: {original_size}")
print(f"清理后数据量: {len(df_clean)}")
print(f"移除数据量: {original_size - len(df_clean)} ({(original_size - len(df_clean))/original_size*100:.2f}%)")

#%%
# ===== 5. 探索性数据分析 (EDA) =====
print("\n=== 探索性数据分析 ===")

# 相关性分析
numeric_columns = ['price', 'sqft_lot', 'sqft_living', 'grade', 'lat', 'long', 
                  'yr_built', 'house_age', 'price_per_sqft', 'years_since_renovation']

correlation_matrix = df_clean[numeric_columns].corr()

plt.figure(figsize=(12, 10))
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, 
            square=True, linewidths=0.5)
plt.title('特征相关性热力图')
plt.tight_layout()
plt.show()

#%%
# 价格与主要特征的关系
fig, axes = plt.subplots(2, 3, figsize=(18, 12))

# 价格 vs 居住面积
axes[0,0].scatter(df_clean['sqft_living'], df_clean['price'], alpha=0.5)
axes[0,0].set_xlabel('居住面积 (sqft)')
axes[0,0].set_ylabel('价格 ($)')
axes[0,0].set_title('价格 vs 居住面积')

# 价格 vs 等级
df_clean.boxplot(column='price', by='grade', ax=axes[0,1])
axes[0,1].set_xlabel('建筑等级')
axes[0,1].set_ylabel('价格 ($)')
axes[0,1].set_title('价格 vs 建筑等级')

# 价格 vs 房屋年龄
axes[0,2].scatter(df_clean['house_age'], df_clean['price'], alpha=0.5)
axes[0,2].set_xlabel('房屋年龄')
axes[0,2].set_ylabel('价格 ($)')
axes[0,2].set_title('价格 vs 房屋年龄')

# 价格 vs 土地面积
axes[1,0].scatter(df_clean['sqft_lot'], df_clean['price'], alpha=0.5)
axes[1,0].set_xlabel('土地面积 (sqft)')
axes[1,0].set_ylabel('价格 ($)')
axes[1,0].set_title('价格 vs 土地面积')

# 价格 vs 纬度
axes[1,1].scatter(df_clean['lat'], df_clean['price'], alpha=0.5)
axes[1,1].set_xlabel('纬度')
axes[1,1].set_ylabel('价格 ($)')
axes[1,1].set_title('价格 vs 纬度')

# 价格 vs 经度
axes[1,2].scatter(df_clean['long'], df_clean['price'], alpha=0.5)
axes[1,2].set_xlabel('经度')
axes[1,2].set_ylabel('价格 ($)')
axes[1,2].set_title('价格 vs 经度')

plt.tight_layout()
plt.show()

#%%
# ===== 6. 特征选择和数据准备 =====
print("\n=== 特征选择和数据准备 ===")

# 选择用于建模的特征
feature_columns = ['sqft_lot', 'sqft_living', 'grade', 'lat', 'long', 
                  'yr_built', 'house_age', 'is_renovated', 'years_since_renovation',
                  'year', 'month']

X = df_clean[feature_columns].copy()
y = df_clean['price'].copy()

print(f"特征数量: {X.shape[1]}")
print(f"样本数量: {X.shape[0]}")
print("选择的特征:", feature_columns)

#%%
# 数据标准化
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

print(f"训练集大小: {X_train.shape}")
print(f"测试集大小: {X_test.shape}")

# 标准化特征
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# MinMax标准化（用于某些算法）
minmax_scaler = MinMaxScaler()
X_train_minmax = minmax_scaler.fit_transform(X_train)
X_test_minmax = minmax_scaler.transform(X_test)

print("数据标准化完成")

#%%
# ===== 7. 机器学习模型训练和评估 =====
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.svm import SVR
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib

print("\n=== 机器学习模型训练 ===")

# 定义评估函数
def evaluate_model(y_true, y_pred, model_name):
    """评估模型性能"""
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    
    print(f"\n{model_name} 模型评估:")
    print(f"均方误差 (MSE): {mse:,.2f}")
    print(f"均方根误差 (RMSE): {rmse:,.2f}")
    print(f"平均绝对误差 (MAE): {mae:,.2f}")
    print(f"决定系数 (R²): {r2:.4f}")
    
    return {'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'R2': r2}

# 存储模型结果
model_results = {}

#%%
# 1. 线性回归
print("训练线性回归模型...")
lr_model = LinearRegression()
lr_model.fit(X_train_scaled, y_train)
lr_pred = lr_model.predict(X_test_scaled)
model_results['Linear Regression'] = evaluate_model(y_test, lr_pred, "线性回归")

#%%
# 2. Ridge回归
print("训练Ridge回归模型...")
ridge_model = Ridge(alpha=1.0, random_state=42)
ridge_model.fit(X_train_scaled, y_train)
ridge_pred = ridge_model.predict(X_test_scaled)
model_results['Ridge'] = evaluate_model(y_test, ridge_pred, "Ridge回归")

#%%
# 3. Lasso回归
print("训练Lasso回归模型...")
lasso_model = Lasso(alpha=1000.0, random_state=42)
lasso_model.fit(X_train_scaled, y_train)
lasso_pred = lasso_model.predict(X_test_scaled)
model_results['Lasso'] = evaluate_model(y_test, lasso_pred, "Lasso回归")

#%%
# 4. 随机森林
print("训练随机森林模型...")
rf_model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
rf_model.fit(X_train, y_train)
rf_pred = rf_model.predict(X_test)
model_results['Random Forest'] = evaluate_model(y_test, rf_pred, "随机森林")

#%%
# 5. 梯度提升
print("训练梯度提升模型...")
gb_model = GradientBoostingRegressor(n_estimators=100, random_state=42)
gb_model.fit(X_train, y_train)
gb_pred = gb_model.predict(X_test)
model_results['Gradient Boosting'] = evaluate_model(y_test, gb_pred, "梯度提升")

#%%
# 6. 支持向量回归
print("训练支持向量回归模型...")
svr_model = SVR(kernel='rbf', C=1000, gamma='scale')
svr_model.fit(X_train_scaled, y_train)
svr_pred = svr_model.predict(X_test_scaled)
model_results['SVR'] = evaluate_model(y_test, svr_pred, "支持向量回归")

#%%
# ===== 8. 模型比较和可视化 =====
print("\n=== 模型性能比较 ===")

# 创建比较表格
comparison_df = pd.DataFrame(model_results).T
comparison_df = comparison_df.round(2)
print(comparison_df)

# 找出最佳模型
best_model_name = comparison_df['R2'].idxmax()
print(f"\n最佳模型: {best_model_name} (R² = {comparison_df.loc[best_model_name, 'R2']:.4f})")

#%%
# 可视化模型比较
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# R²比较
axes[0,0].bar(comparison_df.index, comparison_df['R2'])
axes[0,0].set_title('模型R²比较')
axes[0,0].set_ylabel('R²')
axes[0,0].tick_params(axis='x', rotation=45)

# RMSE比较
axes[0,1].bar(comparison_df.index, comparison_df['RMSE'])
axes[0,1].set_title('模型RMSE比较')
axes[0,1].set_ylabel('RMSE')
axes[0,1].tick_params(axis='x', rotation=45)

# MAE比较
axes[1,0].bar(comparison_df.index, comparison_df['MAE'])
axes[1,0].set_title('模型MAE比较')
axes[1,0].set_ylabel('MAE')
axes[1,0].tick_params(axis='x', rotation=45)

# MSE比较
axes[1,1].bar(comparison_df.index, comparison_df['MSE'])
axes[1,1].set_title('模型MSE比较')
axes[1,1].set_ylabel('MSE')
axes[1,1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

#%%
# 预测值 vs 实际值可视化
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
predictions = [lr_pred, ridge_pred, lasso_pred, rf_pred, gb_pred, svr_pred]
model_names = ['Linear Regression', 'Ridge', 'Lasso', 'Random Forest', 'Gradient Boosting', 'SVR']

for i, (pred, name) in enumerate(zip(predictions, model_names)):
    row = i // 3
    col = i % 3

    axes[row, col].scatter(y_test, pred, alpha=0.5)
    axes[row, col].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    axes[row, col].set_xlabel('实际价格')
    axes[row, col].set_ylabel('预测价格')
    axes[row, col].set_title(f'{name}')

    # 添加R²值
    r2 = model_results[name]['R2']
    axes[row, col].text(0.05, 0.95, f'R² = {r2:.3f}', transform=axes[row, col].transAxes,
                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

plt.tight_layout()
plt.show()

#%%
# ===== 9. 特征重要性分析 =====
print("\n=== 特征重要性分析 ===")

# 随机森林特征重要性
rf_importance = pd.DataFrame({
    '特征': feature_columns,
    '重要性': rf_model.feature_importances_
}).sort_values('重要性', ascending=False)

print("随机森林特征重要性:")
print(rf_importance)

# 梯度提升特征重要性
gb_importance = pd.DataFrame({
    '特征': feature_columns,
    '重要性': gb_model.feature_importances_
}).sort_values('重要性', ascending=False)

print("\n梯度提升特征重要性:")
print(gb_importance)

#%%
# 可视化特征重要性
fig, axes = plt.subplots(1, 2, figsize=(16, 6))

# 随机森林特征重要性
axes[0].barh(rf_importance['特征'], rf_importance['重要性'])
axes[0].set_title('随机森林 - 特征重要性')
axes[0].set_xlabel('重要性')

# 梯度提升特征重要性
axes[1].barh(gb_importance['特征'], gb_importance['重要性'])
axes[1].set_title('梯度提升 - 特征重要性')
axes[1].set_xlabel('重要性')

plt.tight_layout()
plt.show()

#%%
# ===== 10. 残差分析 =====
print("\n=== 残差分析 ===")

# 选择最佳模型进行残差分析
if best_model_name == 'Random Forest':
    best_pred = rf_pred
    best_model = rf_model
elif best_model_name == 'Gradient Boosting':
    best_pred = gb_pred
    best_model = gb_model
elif best_model_name == 'Linear Regression':
    best_pred = lr_pred
    best_model = lr_model
elif best_model_name == 'Ridge':
    best_pred = ridge_pred
    best_model = ridge_model
elif best_model_name == 'Lasso':
    best_pred = lasso_pred
    best_model = lasso_model
else:
    best_pred = svr_pred
    best_model = svr_model

residuals = y_test - best_pred

# 残差分析图
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# 残差 vs 预测值
axes[0,0].scatter(best_pred, residuals, alpha=0.5)
axes[0,0].axhline(y=0, color='r', linestyle='--')
axes[0,0].set_xlabel('预测值')
axes[0,0].set_ylabel('残差')
axes[0,0].set_title('残差 vs 预测值')

# 残差直方图
axes[0,1].hist(residuals, bins=50, alpha=0.7)
axes[0,1].set_xlabel('残差')
axes[0,1].set_ylabel('频次')
axes[0,1].set_title('残差分布')

# Q-Q图
from scipy import stats
stats.probplot(residuals, dist="norm", plot=axes[1,0])
axes[1,0].set_title('残差Q-Q图')

# 残差 vs 实际值
axes[1,1].scatter(y_test, residuals, alpha=0.5)
axes[1,1].axhline(y=0, color='r', linestyle='--')
axes[1,1].set_xlabel('实际值')
axes[1,1].set_ylabel('残差')
axes[1,1].set_title('残差 vs 实际值')

plt.tight_layout()
plt.show()

#%%
# ===== 11. 模型保存 =====
print("\n=== 模型保存 ===")

# 创建模型目录
model_dir = 'model'
if not os.path.exists(model_dir):
    os.makedirs(model_dir)

# 保存最佳模型
best_model_path = os.path.join(model_dir, f'best_model_{best_model_name.lower().replace(" ", "_")}.pkl')
joblib.dump(best_model, best_model_path)
print(f"最佳模型已保存到: {best_model_path}")

# 保存标准化器
scaler_path = os.path.join(model_dir, 'scaler.pkl')
joblib.dump(scaler, scaler_path)
print(f"标准化器已保存到: {scaler_path}")

# 保存特征列名
feature_path = os.path.join(model_dir, 'feature_columns.pkl')
joblib.dump(feature_columns, feature_path)
print(f"特征列名已保存到: {feature_path}")

# 保存模型比较结果
results_path = os.path.join(model_dir, 'model_comparison.csv')
comparison_df.to_csv(results_path)
print(f"模型比较结果已保存到: {results_path}")

#%%
# ===== 12. 业务洞察和总结 =====
print("\n=== 业务洞察和总结 ===")

print("1. 数据质量:")
print(f"   - 原始数据: {original_size:,} 条记录")
print(f"   - 清理后数据: {len(df_clean):,} 条记录")
print(f"   - 数据清理率: {(original_size - len(df_clean))/original_size*100:.1f}%")

print("\n2. 模型性能:")
print(f"   - 最佳模型: {best_model_name}")
print(f"   - R²得分: {comparison_df.loc[best_model_name, 'R2']:.4f}")
print(f"   - RMSE: ${comparison_df.loc[best_model_name, 'RMSE']:,.0f}")
print(f"   - MAE: ${comparison_df.loc[best_model_name, 'MAE']:,.0f}")

print("\n3. 关键特征 (基于随机森林):")
for i, row in rf_importance.head(5).iterrows():
    print(f"   - {row['特征']}: {row['重要性']:.3f}")

print("\n4. 业务建议:")
print("   - 居住面积是影响房价的最重要因素")
print("   - 建筑等级和地理位置也显著影响房价")
print("   - 模型可用于房价估算，但需要考虑市场变化")
print("   - 建议定期更新模型以适应市场变化")

#%%
