#%%
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示为方块的问题

#%%
# 加载数据
df = pd.read_csv('data.csv')
print(f"数据形状: {df.shape}")

#%%
# 测试箱线图修复
fig, axes = plt.subplots(2, 2, figsize=(12, 8))

# 价格分布
axes[0,0].hist(df['price'], bins=50, alpha=0.7)
axes[0,0].set_title('房价分布')
axes[0,0].set_xlabel('价格 ($)')
axes[0,0].set_ylabel('频次')

# 价格箱线图 - 修复后
axes[0,1].boxplot([df['price']])
axes[0,1].set_title('房价箱线图')
axes[0,1].set_ylabel('价格 ($)')
axes[0,1].set_xticklabels(['价格'])

# 居住面积分布
axes[1,0].hist(df['sqft_living'], bins=50, alpha=0.7)
axes[1,0].set_title('居住面积分布')
axes[1,0].set_xlabel('面积 (sqft)')
axes[1,0].set_ylabel('频次')

# 居住面积箱线图 - 修复后
axes[1,1].boxplot([df['sqft_living']])
axes[1,1].set_title('居住面积箱线图')
axes[1,1].set_ylabel('面积 (sqft)')
axes[1,1].set_xticklabels(['面积'])

plt.tight_layout()
plt.show()

print("箱线图测试完成！")

#%%
# 额外测试：按等级分组的价格箱线图
plt.figure(figsize=(12, 6))
df.boxplot(column='price', by='grade', figsize=(12, 6))
plt.title('按建筑等级分组的房价箱线图')
plt.suptitle('')  # 移除默认的标题
plt.xlabel('建筑等级')
plt.ylabel('价格 ($)')
plt.show()

print("分组箱线图测试完成！")
