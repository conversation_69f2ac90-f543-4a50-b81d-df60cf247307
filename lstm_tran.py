#%%
import numpy as np
import pandas as pd
import os
#%%
for dirname, _, filenames in os.walk('archive/'):
    for filename in filenames:
        print(os.path.join(dirname, filename))
#%%
df=pd.read_csv('archive/BHARTIARTL.csv',na_values=['null'],
               index_col='Date',parse_dates=True,infer_datetime_format=True)
df.head()
#%%
from matplotlib import pyplot as plt
# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示为方块的问题
df['Close'].plot(kind='line', figsize=(8, 4), title='收盘价')
plt.gca().spines[['top', 'right']].set_visible(False)
#%%
#设置目标变量
output_var = pd.DataFrame(df['Close'])
#特征值
features = ['Open', 'High', 'Low', 'Volume']
#%%
from sklearn.preprocessing import MinMaxScaler
scaler = MinMaxScaler()
feature_transform = scaler.fit_transform(df[features])
feature_transform= pd.DataFrame(columns=features, data=feature_transform, index=df.index)
feature_transform.head()
#%%
feature_transform.shape
#%%
from sklearn.model_selection import TimeSeriesSplit
#拆分训练集和测试集
timesplit= TimeSeriesSplit(n_splits=10) # 90-10%
for train_index, test_index in timesplit.split(feature_transform):
        X_train, X_test = feature_transform[:len(train_index)], feature_transform[len(train_index): (len(train_index)+len(test_index))]
        y_train, y_test = output_var[:len(train_index)].values.ravel(), output_var[len(train_index): (len(train_index)+len(test_index))].values.ravel()
#%%
print(X_train.shape)
print(X_test.shape)
#%%
trainX =np.array(X_train)
testX =np.array(X_test)
X_train = trainX.reshape(X_train.shape[0], 1, X_train.shape[1])
X_test = testX.reshape(X_test.shape[0], 1, X_test.shape[1])
print(X_train.shape)
print(X_test.shape)
#%%
#构建LSTM模型
import tensorflow as tf
lstm = tf.keras.Sequential()
lstm.add(tf.keras.layers.LSTM(32, input_shape=(1, trainX.shape[1]), activation='relu', return_sequences=False))
lstm.add(tf.keras.layers.Dense(1))
lstm.compile(loss='mean_squared_error', optimizer='adam',metrics = ['mse'])
tf.keras.utils.plot_model(lstm, show_shapes=True, show_layer_names=True)
#%%
train_metrics=lstm.fit(X_train, y_train, epochs=100, batch_size=10, verbose=1, shuffle=False,validation_data=(X_test, y_test))
#%%
history_df = pd.DataFrame(train_metrics.history)
history_df
#%%
y_pred= lstm.predict(X_test)
#%%
from sklearn.metrics import mean_squared_error
mse = mean_squared_error(y_test, y_pred)
print("Mean Squared Error:", mse)
#%%
import matplotlib.pyplot as plt
plt.plot(y_test, label='实际值')
plt.plot(y_pred, label='预测值')
plt.title('LSTM预测')
plt.xlabel('时间')
plt.ylabel('价格')
plt.legend()
plt.show()
#%%
# 保存模型到model目录
model_dir = 'model'
# 检查目录是否存在，如果不存在则创建
if not os.path.exists(model_dir):
    os.makedirs(model_dir)

# 保存模型（添加 .keras 扩展名）
model_path = os.path.join(model_dir, 'lstm_model.keras')  # 推荐使用 .keras 格式
lstm.save(model_path)
print(f"模型已保存到: {model_path}")

# 保存scaler以便后续使用
import joblib
scaler_path = os.path.join(model_dir, 'scaler.pkl')
joblib.dump(scaler, scaler_path)
print(f"特征缩放器已保存到: {scaler_path}")
#%%
# 随机森林模型预测
from sklearn.ensemble import RandomForestRegressor

# 使用原始特征数据（未reshape的数据）
rf_model = RandomForestRegressor(n_estimators=100, random_state=42)
rf_model.fit(trainX, y_train)

# 预测
rf_pred = rf_model.predict(testX)

# 评估随机森林模型
rf_mse = mean_squared_error(y_test, rf_pred)
print("随机森林 Mean Squared Error:", rf_mse)
print("LSTM Mean Squared Error:", mse)
#%%
# 可视化对比两种模型的预测结果
plt.figure(figsize=(12, 6))
plt.plot(y_test, label='实际值')
plt.plot(y_pred, label='LSTM预测值')
plt.plot(rf_pred, label='随机森林预测值')
plt.title('LSTM vs 随机森林预测对比')
plt.xlabel('时间')
plt.ylabel('价格')
plt.legend()
plt.show()
#%%
# 特征重要性分析
feature_importance = pd.DataFrame({
    '特征': features,
    '重要性': rf_model.feature_importances_
})
feature_importance = feature_importance.sort_values('重要性', ascending=False)

plt.figure(figsize=(10, 6))
plt.bar(feature_importance['特征'], feature_importance['重要性'])
plt.title('随机森林模型 - 特征重要性')
plt.xlabel('特征')
plt.ylabel('重要性')
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()
#%%
# 保存随机森林模型
rf_model_path = os.path.join(model_dir, 'rf_model.pkl')
joblib.dump(rf_model, rf_model_path)
print(f"随机森林模型已保存到: {rf_model_path}")
#%%
# 在评估模型部分添加更多的评估指标
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import numpy as np

# 评估LSTM模型
y_pred_lstm = lstm.predict(X_test)
mse_lstm = mean_squared_error(y_test, y_pred_lstm)
rmse_lstm = np.sqrt(mse_lstm)
mae_lstm = mean_absolute_error(y_test, y_pred_lstm)
r2_lstm = r2_score(y_test, y_pred_lstm)

print("\n===== LSTM模型评估 =====")
print(f"均方误差 (MSE): {mse_lstm:.4f}")
print(f"均方根误差 (RMSE): {rmse_lstm:.4f}")
print(f"平均绝对误差 (MAE): {mae_lstm:.4f}")
print(f"决定系数 (R²): {r2_lstm:.4f}")

# 计算方向准确率（预测涨跌方向是否正确）
def direction_accuracy(y_true, y_pred, prev_prices):
    correct_direction = 0
    total = len(y_true) - 1
    
    for i in range(1, len(y_true)):
        actual_direction = y_true[i] > prev_prices[i]
        predicted_direction = y_pred[i-1] > prev_prices[i]
        
        if actual_direction == predicted_direction:
            correct_direction += 1
    
    return correct_direction / total if total > 0 else 0

# 获取前一天的价格
prev_prices = output_var[len(train_index):].values.ravel()[:-1]
prev_prices = np.append(output_var[len(train_index)-1:len(train_index)].values.ravel(), prev_prices)

# 计算LSTM的方向准确率
direction_acc_lstm = direction_accuracy(y_test, y_pred_lstm, prev_prices)
print(f"方向准确率: {direction_acc_lstm:.4f} (预测涨跌方向的准确率)")

# 评估随机森林模型
rf_pred = rf_model.predict(testX)
mse_rf = mean_squared_error(y_test, rf_pred)
rmse_rf = np.sqrt(mse_rf)
mae_rf = mean_absolute_error(y_test, rf_pred)
r2_rf = r2_score(y_test, rf_pred)

print("\n===== 随机森林模型评估 =====")
print(f"均方误差 (MSE): {mse_rf:.4f}")
print(f"均方根误差 (RMSE): {rmse_rf:.4f}")
print(f"平均绝对误差 (MAE): {mae_rf:.4f}")
print(f"决定系数 (R²): {r2_rf:.4f}")

# 计算随机森林的方向准确率
direction_acc_rf = direction_accuracy(y_test, rf_pred, prev_prices)
print(f"方向准确率: {direction_acc_rf:.4f} (预测涨跌方向的准确率)")

# 模型比较
print("\n===== 模型比较 =====")
print(f"{'指标':<15} {'LSTM':<15} {'随机森林':<15}")
print("-" * 45)
print(f"{'MSE':<15} {mse_lstm:<15.4f} {mse_rf:<15.4f}")
print(f"{'RMSE':<15} {rmse_lstm:<15.4f} {rmse_rf:<15.4f}")
print(f"{'MAE':<15} {mae_lstm:<15.4f} {mae_rf:<15.4f}")
print(f"{'R²':<15} {r2_lstm:<15.4f} {r2_rf:<15.4f}")
print(f"{'方向准确率':<15} {direction_acc_lstm:<15.4f} {direction_acc_rf:<15.4f}")

# 可视化对比两种模型的预测结果
plt.figure(figsize=(12, 6))
plt.plot(y_test, label='实际值', color='black', linewidth=2)
plt.plot(y_pred_lstm, label='LSTM预测值', color='blue', linestyle='--')
plt.plot(rf_pred, label='随机森林预测值', color='red', linestyle='-.')
plt.title('LSTM vs 随机森林预测对比')
plt.xlabel('时间')
plt.ylabel('价格')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.7)
plt.tight_layout()
plt.show()
#%%
