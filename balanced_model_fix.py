#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

#%%
print("=== 平衡的模型修复 ===")

# 加载和预处理数据
df = pd.read_csv('data.csv')
print(f"原始数据形状: {df.shape}")

# 数据预处理
df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')
df['year'] = df['date'].dt.year
df['month'] = df['date'].dt.month
df['day_of_year'] = df['date'].dt.dayofyear

df['is_renovated'] = (df['yr_renovated'] > 0).astype(int)
df['years_since_renovation'] = np.where(df['yr_renovated'] > 0, 
                                       df['year'] - df['yr_renovated'], 
                                       df['year'] - df['yr_built'])

df['house_age'] = df['year'] - df['yr_built']
df['price_per_sqft'] = df['price'] / df['sqft_living']

# 添加更多有用的特征
df['living_lot_ratio'] = df['sqft_living'] / df['sqft_lot']
df['log_sqft_living'] = np.log1p(df['sqft_living'])
df['log_sqft_lot'] = np.log1p(df['sqft_lot'])

# 数据清理
price_99_5th = df['price'].quantile(0.995)
price_0_5th = df['price'].quantile(0.005)
df_clean = df[(df['price'] >= price_0_5th) & (df['price'] <= price_99_5th)]

living_99_5th = df['sqft_living'].quantile(0.995)
living_0_5th = df['sqft_living'].quantile(0.005)
df_clean = df_clean[(df_clean['sqft_living'] >= living_0_5th) & (df_clean['sqft_living'] <= living_99_5th)]

df_clean = df_clean[df_clean['price'] > 0]
df_clean = df_clean[df_clean['sqft_living'] > 0]
df_clean = df_clean[df_clean['sqft_lot'] > 0]

print(f"清理后数据形状: {df_clean.shape}")

#%%
# 测试不同的特征组合
print("\n=== 测试不同特征组合 ===")

def evaluate_model(y_true, y_pred, model_name):
    """评估模型性能"""
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
    
    print(f"\n{model_name}:")
    print(f"  RMSE: ${rmse:,.0f}")
    print(f"  MAE: ${mae:,.0f}")
    print(f"  R²: {r2:.4f}")
    print(f"  MAPE: {mape:.2f}%")
    
    return {'RMSE': rmse, 'MAE': mae, 'R2': r2, 'MAPE': mape}

def test_feature_set(features, set_name, use_log=False):
    """测试特征集合"""
    X = df_clean[features].copy()
    y = df_clean['price'].copy()
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # 标准化特征
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    results = {}
    
    # 线性回归
    if use_log:
        y_train_log = np.log(y_train)
        lr = LinearRegression()
        lr.fit(X_train_scaled, y_train_log)
        lr_pred_log = lr.predict(X_test_scaled)
        lr_pred = np.exp(lr_pred_log)
    else:
        lr = LinearRegression()
        lr.fit(X_train_scaled, y_train)
        lr_pred = lr.predict(X_test_scaled)
    
    results[f'{set_name}_LinearRegression'] = evaluate_model(y_test, lr_pred, f"{set_name} - 线性回归")
    
    # 随机森林
    rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
    rf.fit(X_train, y_train)
    rf_pred = rf.predict(X_test)
    results[f'{set_name}_RandomForest'] = evaluate_model(y_test, rf_pred, f"{set_name} - 随机森林")
    
    return results

#%%
# 特征集合定义
feature_sets = {
    '原始全部': ['sqft_lot', 'sqft_living', 'grade', 'lat', 'long', 
               'yr_built', 'house_age', 'is_renovated', 'years_since_renovation',
               'year', 'month'],
    
    '移除冗余': ['sqft_lot', 'sqft_living', 'grade', 'lat', 'long', 
               'house_age', 'is_renovated'],  # 移除yr_built, years_since_renovation, year, month
    
    '核心特征': ['sqft_living', 'grade', 'lat', 'long', 'house_age'],  # 最重要的5个
    
    '增强特征': ['sqft_living', 'grade', 'lat', 'long', 'house_age', 
               'is_renovated', 'living_lot_ratio', 'log_sqft_living'],  # 添加衍生特征
    
    '最优组合': ['sqft_living', 'grade', 'lat', 'long', 'house_age', 
               'is_renovated', 'sqft_lot']  # 平衡的组合
}

#%%
# 测试所有特征集合
all_results = {}

for set_name, features in feature_sets.items():
    print(f"\n{'='*50}")
    print(f"测试特征集: {set_name} ({len(features)}个特征)")
    print(f"特征: {features}")
    
    # 测试不使用对数变换
    results_normal = test_feature_set(features, f"{set_name}(普通)", use_log=False)
    all_results.update(results_normal)
    
    # 测试使用对数变换
    results_log = test_feature_set(features, f"{set_name}(对数)", use_log=True)
    all_results.update(results_log)

#%%
# 结果汇总
print("\n" + "="*80)
print("=== 所有结果汇总 ===")
results_df = pd.DataFrame(all_results).T
results_df = results_df.round(2)

# 按RMSE排序
results_df_sorted = results_df.sort_values('RMSE')
print("\n按RMSE排序的结果:")
print(results_df_sorted)

# 找出最佳配置
best_config = results_df_sorted.index[0]
best_rmse = results_df_sorted.iloc[0]['RMSE']
best_r2 = results_df_sorted.iloc[0]['R2']

print(f"\n🏆 最佳配置: {best_config}")
print(f"   RMSE: ${best_rmse:,.0f}")
print(f"   R²: {best_r2:.4f}")

#%%
# 可视化结果
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# 只显示线性回归结果以便比较
lr_results = results_df[results_df.index.str.contains('LinearRegression')]

# RMSE比较
axes[0,0].bar(range(len(lr_results)), lr_results['RMSE'])
axes[0,0].set_title('线性回归 - RMSE比较')
axes[0,0].set_ylabel('RMSE ($)')
axes[0,0].set_xticks(range(len(lr_results)))
axes[0,0].set_xticklabels(lr_results.index, rotation=45, ha='right')

# R²比较
axes[0,1].bar(range(len(lr_results)), lr_results['R2'])
axes[0,1].set_title('线性回归 - R²比较')
axes[0,1].set_ylabel('R²')
axes[0,1].set_xticks(range(len(lr_results)))
axes[0,1].set_xticklabels(lr_results.index, rotation=45, ha='right')

# 随机森林结果
rf_results = results_df[results_df.index.str.contains('RandomForest')]

# RMSE比较
axes[1,0].bar(range(len(rf_results)), rf_results['RMSE'])
axes[1,0].set_title('随机森林 - RMSE比较')
axes[1,0].set_ylabel('RMSE ($)')
axes[1,0].set_xticks(range(len(rf_results)))
axes[1,0].set_xticklabels(rf_results.index, rotation=45, ha='right')

# R²比较
axes[1,1].bar(range(len(rf_results)), rf_results['R2'])
axes[1,1].set_title('随机森林 - R²比较')
axes[1,1].set_ylabel('R²')
axes[1,1].set_xticks(range(len(rf_results)))
axes[1,1].set_xticklabels(rf_results.index, rotation=45, ha='right')

plt.tight_layout()
plt.show()

#%%
# 推荐最佳配置
print("\n" + "="*80)
print("=== 推荐配置 ===")

# 找出线性回归的最佳配置
best_lr = lr_results.sort_values('RMSE').index[0]
best_lr_rmse = lr_results.sort_values('RMSE').iloc[0]['RMSE']
best_lr_r2 = lr_results.sort_values('RMSE').iloc[0]['R2']

# 找出随机森林的最佳配置
best_rf = rf_results.sort_values('RMSE').index[0]
best_rf_rmse = rf_results.sort_values('RMSE').iloc[0]['RMSE']
best_rf_r2 = rf_results.sort_values('RMSE').iloc[0]['R2']

print(f"最佳线性回归配置: {best_lr}")
print(f"  RMSE: ${best_lr_rmse:,.0f}, R²: {best_lr_r2:.4f}")

print(f"\n最佳随机森林配置: {best_rf}")
print(f"  RMSE: ${best_rf_rmse:,.0f}, R²: {best_rf_r2:.4f}")

# 与原始结果比较
original_rmse = 184261  # 您提到的原始RMSE
print(f"\n📊 与原始模型比较:")
print(f"   原始RMSE: ${original_rmse:,.0f}")
print(f"   最佳RMSE: ${best_rmse:,.0f}")
improvement = (original_rmse - best_rmse) / original_rmse * 100
print(f"   改进幅度: {improvement:.1f}%")

print("\n平衡模型修复完成！")
